/* eslint-disable @typescript-eslint/no-explicit-any */
import React from "react";
// import { Dropdown } from "react-bootstrap";
import Dropdown from "../../Dropdown/Dropdown";
import styled from "styled-components";
// import FormControl from "../../Form/FormControl/FormControl";
// import Icon from "../../Icon";
// import Layout from "../../Layout/Layout";
// import Text from "../../Text/Text";
import Div from "../../Div";
import { Text } from "@repo/ui/components/Text";
import { ChevronDownIcon, ChevronUpIcon } from "lucide-react";
import Search from "@repo/ui/components/search";

const DropdownMenu = styled(Dropdown.Menu)`
  padding: 0px;
  min-width: 5rem;
  background: #ffffff;
  /* white */

  border: 1px solid #fbfbfb;
  box-sizing: border-box;
  box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.14);
  border-radius: 2px;
`;

const DropdownItem = styled(Dropdown.Item)`
  &:hover {
    background-color: #fff !important;
  }
  &:focus {
    background-color: #fff !important;
  }
`;

const FilterMultiSelect = (props: any) => {
  const [show, setShow] = React.useState(false);
  const identifier = props.identifier || "key";

  const onSelect = (item: any) => {
    const list = props.selected;
    if (checkSelected(item)) {
      const index = list.findIndex((o: string) => o === item.key);
      list.splice(index, 1);
    } else {
      list.push(item[identifier]);
    }
    props.onOptionsSelect([...list]);
  };

  const checkSelected = (item: string) => {
    return props.selected.includes(item[identifier]);
  };

  const getSelectedLabels = () => {
    if (props.selected.length) {
      return (
        <Text.P2 className="pr-2">{props.selected.length} Selected</Text.P2>
      );
    }
    return <Text.P2 className="pr-2">All</Text.P2>;
  };

  const getSearchTemplate = () => {
    if (!props.hasSearch) return;
    return (
      // <DropdownItem onClick={() => ""} className="m-0 p-1">
      //   <FormControl.Search placeholder="Search" />
      // </DropdownItem>
      <Search placeholder="Search" onChange={() => ""} />
    );
  };

  return (
    <Dropdown
      show={show}
      onToggle={() => {
        setShow(!show);
      }}
    >
      <Dropdown.Toggle
        variant="white"
        block
        className={"text-left border py-2"}
      >
        <div className="flex justify-between items-center">
          <Text.H3 className="mr-auto fs-12">{props.title}</Text.H3>
          {getSelectedLabels()}
          {/* <Icon
            className="text-muted fs-12 mt-1"
            icon={show ? "fal fa-chevron-up" : "fal fa-chevron-down"}
          /> */}
          {show ? (
            <ChevronUpIcon className="text-muted text-xs self-center " />
          ) : (
            <ChevronDownIcon className="text-muted text-xs self-center" />
          )}
        </div>
      </Dropdown.Toggle>
      {/* show={props.show} */}
      <DropdownMenu className="w-100">
        {getSearchTemplate()}
        {props.options.map((item: any) => {
          const checked = checkSelected(item);
          return (
            <DropdownItem
              as={Div}
              onClick={() => onSelect(item)}
              className="p-1 m-0 cursor-pointer"
              key={item.key}
            >
              {/* <FormControl.Checkbox>
                <FormControl.Checkbox.Input
                  checked={checked}
                  onChange={() => ""}
                  className="mt-2"
                />
                <FormControl.Checkbox.Label>
                  <Text.P1>{item.title}</Text.P1>
                </FormControl.Checkbox.Label>
              </FormControl.Checkbox> */}
            </DropdownItem>
          );
        })}
      </DropdownMenu>
    </Dropdown>
  );
};

export default FilterMultiSelect;
