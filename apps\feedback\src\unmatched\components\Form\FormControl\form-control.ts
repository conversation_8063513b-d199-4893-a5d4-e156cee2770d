import React from "react";
import _ from "lodash";
// import classes from "../Forms.module.scss";

export type FormControlSize = "sm" | "lg" | number | undefined;

export interface FormControlProps
  extends React.AllHTMLAttributes<
    HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
  > {
  className?: string;
  disabled?: boolean;
  isValid?: boolean;
  isInvalid?: boolean;
  size?: any;
  formik?: any;
}

export const CUSTOM_PROPS = [
  "size",
  "isValid",
  "isInvalid",
  "list",
  "children",
  "setValue",
];

export const INPUT_TYPES = {
  TEXT: "text",
  NUMBER: "number",
  EMAIL: "email",
  PASSWORD: "password",
  TEXT_AREA: "text-area",
  RADIO: "radio",
  CHECKBOX: "checkbox",
  SELECT: "select",
  DATE: "date",
};

// Form Inputs

export const getInputClass = (data: {
  size?: FormControlSize;
  className?: string;
  isValid?: boolean;
  isInvalid?: boolean;
}) => {
  const { size, isInvalid, isValid, className } = data;
  const defaultClass = "form-control";
  const formControlClass = size
    ? `${defaultClass} form-control-${size}`
    : defaultClass;
  const validClass = isValid ? "is-valid" : "";
  const inValidClass = isInvalid ? "is-invalid" : "";
  return `${formControlClass} ${validClass} ${inValidClass} ${className}`;
};

export const getCheckClass = (data: { className?: string }) => {
  const { className } = data;
  const defaultClass = "form-check-input";
  return `${defaultClass} ${className}`;
};

export const getFormControlAttributes = (props: FormControlProps, ref: any) => {
  return {
    ..._.omit(props, CUSTOM_PROPS),
    ref,
    className: getInputClass(_.pick(props, [...CUSTOM_PROPS, "className"])),
  };
};

export const getCheckAttributes = (props: FormControlProps, ref: any) => {
  return {
    ..._.omit(props, CUSTOM_PROPS),
    ref,
    className: getCheckClass(_.pick(props, ["className"])),
  };
};

export const formControlDefaultProps = {
  className: "",
  size: "",
};
