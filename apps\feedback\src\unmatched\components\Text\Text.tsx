import styled from "styled-components";

const H1 = styled.div`
  font-size: 1.5rem;
  font-family: Inter;
  font-style: normal;
  font-weight: 700;
  line-height: 1.875rem;
`;

const H2 = styled.div`
  font-size: 1.125rem;
  font-family: Inter;
  font-style: normal;
  font-weight: 600;
  line-height: 1.406rem;
`;

const H3 = styled.div`
  font-size: 0.875rem;
  font-family: Inter;
  font-style: normal;
  font-weight: 600;
  line-height: 1.059rem;
`;

const H4 = styled.div`
  font-size: 0.75rem;
  font-family: Inter;
  font-style: normal;
  font-weight: 600;
  line-height: 0.938rem;
`;

const P1 = styled.div`
  font-size: 0.815rem;
  font-family: Inter;
  font-weight: 400;
  font-style: normal;
  line-height: 1.213rem;
`;

const P2 = styled.div`
  font-size: 0.75rem;
  font-family: Inter;
  font-style: normal;
  font-weight: 400;
  line-height: 1.125rem;
`;

const L1 = styled.div`
  font-size: 0.625rem;
  line-height: 0.756rem;
  font-family: Inter;
  font-style: normal;
  font-weight: normal;
  color: #838383;
`;

const InputLabel = styled.div`
  font-size: 0.875rem;
  line-height: 1.059rem;
  font-family: Inter;
  font-style: normal;
  font-weight: 400;
  color: #6d6d6d !important;
`;

const Text = () => {
  return <></>;
};

Text.H1 = H1;
Text.H2 = H2;
Text.H3 = H3;
Text.H4 = H4;

Text.P1 = P1;
Text.P2 = P2;

Text.L1 = L1;
Text.InputLabel = InputLabel;

export default Text;
