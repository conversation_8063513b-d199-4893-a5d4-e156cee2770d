import { omit } from "lodash";
// import getUtilClassName from "../utils/styles";

const CUSTOM_PROPS = ["children", "styleOptions", "isActive"];

// const setClassNames = (className: string, styleOptions: any) => {
//   const styles = getUtilClassName(styleOptions || {});
//   return `${styles} ${className}`;
// };

const Div = (props: React.HTMLAttributes<HTMLDivElement>) => {
  const attributes = {
    ...omit(props, CUSTOM_PROPS),
    className: props.className,
  };
  return <div {...attributes}>{props.children}</div>;
};

Div.defaultProps = {};

export default Div;
