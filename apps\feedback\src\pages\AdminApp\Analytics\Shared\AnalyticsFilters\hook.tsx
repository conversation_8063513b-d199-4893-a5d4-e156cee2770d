import React from "react";
import util from "unmatched/utils";
import { getAnalyticsFiltersFact } from "./api";

const useAnalyticsFilters = () => {
  const [selected, setSelected] = React.useState({
    years: [],
    surveys: [],
    types: [],
    raterGroups: [],
    categories: [],
  });
  // const [year, setYear] = React.useState("");
  const [years, setYears] = React.useState([]);

  const [meta, setMeta] = React.useState([]);

  // const [survey, setSurvey]: any = React.useState({});
  const [surveys, setSurveys] = React.useState([]);
  const [surveysData, setSurveysData] = React.useState([]);

  // const [type, setType]: any = React.useState({});
  const [types, setTypes] = React.useState([]);

  const [groups, setGroups]: any = React.useState([]);
  // const [group, setGroup]: any = React.useState([]);

  const [categories, setCategories]: any = React.useState([]);
  // const [category, setCategory]: any = React.useState([]);

  const getFilters = (params: any) => {
    return getAnalyticsFiltersFact(params);
  };

  const setFilters = (response: any) => {
    setSelected({
      ...selected,
      ...response.selected,
    });
    setYears(response.years);
    setSurveys(response.surveys);
    setTypes(response.types);
    setGroups(response.groups);
    setCategories(response.categories);
    setSurveysData(response.surveysData);
    setMeta(response.meta);
  };

  const getFilterParams = (_filters: any = {}) => {
    return _filters.years
      ? {
          year: _filters.years?.length ? _filters.years[0] : "",
          resource_types: util.getCommaSeperatedFromArray(_filters.types || []),
        }
      : {};
  };

  // const onYearChange = () => '';

  // const onSurveyChange = () => '';

  // const onTypeChange = () => '';

  // const onGroupChange = () => '';

  // const onCategoryChange = () => '';

  React.useEffect(() => {
    // getFilters({});
  }, []);

  return {
    selected,
    categories,
    groups,
    surveys,
    surveysData,
    years,
    types,
    meta,
    // onYearChange,
    // onSurveyChange,
    // onTypeChange,
    // onGroupChange,
    // onCategoryChange,
    getFilters,
    setFilters,
    getFilterParams,
  };
};

export default useAnalyticsFilters;
