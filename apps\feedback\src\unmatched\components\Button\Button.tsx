/* eslint-disable @typescript-eslint/no-explicit-any */
import React from "react";
// import classes from "./Button.module.scss";
import {Button as ButtonUI} from "@repo/ui/components/Button";

export type ButtonType = "button" | "reset" | "submit";
export type ButtonVariant =
  | "default"
  | "secondary"
  | "success"
  | "danger"
  | "light"
  | "link";
export type ButtonSize = "sm" | "lg";

export interface ButtonProps extends React.ButtonHTMLAttributes<HTMLElement> {
  active?: boolean;
  block?: boolean;
  variant?: ButtonVariant;
  size?: ButtonSize;
  type?: ButtonType;
  href?: string;
  disabled?: boolean;
  loading?: boolean;
  target?: any;
  to?: any;
  title?: string;
  stopPropagation: boolean;
  preventDefault: boolean;
  as?: any;
}

const defaultProps = {
  variant: "default",
  active: false,
  disabled: false,
  loading: false,
  block: false,
  target: "",
  className: "",
};

const onButtonClick = (e: any, props: ButtonProps) => {
  const { onClick, stopPropagation, preventDefault } = props;
  if (!onClick) return;
  if (stopPropagation) e.stopPropagation();
  if (preventDefault) e.preventDefault();
  onClick(e);
};

const getButtonClassName = (props: ButtonProps) => {
  const { variant, block, size, className } = props;
  const btnClass = `Custom-Button btn btn-${variant}`;
  const blockClass = block ? 'btn-block' : "";
  const sizeClass = size ? `btn-${size}` : "";
  return `${btnClass} ${blockClass} ${sizeClass} ${className}`;
};

const Button: any = React.forwardRef((props: ButtonProps, ref: any) => {
  const { disabled, type, loading, href, target, title, style, to } = props;
  const buttonClass = getButtonClassName(props);
  if (href) {
    return (
      <a
        ref={ref}
        title={title}
        style={style}
        target={target}
        className={buttonClass}
        href={href}
      >
        {props.children}
      </a>
    );
  } else if (to) {
    if (props.as) {
      return <props.as
        ref={ref}
        title={title}
        style={style}
        className={buttonClass}
        to={to}
      >
        {props.children}
      </props.as>
    }
    return (
      <a
        ref={ref}
        title={title}
        style={style}
        className={buttonClass}
        href={`${window.location.origin}/${to}`}
      >
        {props.children}
      </a>
    );
  }
  return (
    <ButtonUI
      ref={ref}
      style={style}
      title={title}
      disabled={disabled || loading}
      type={type}
      className={buttonClass}
      onClick={(e: any) => {
        onButtonClick(e, props);
      }}
    >
      {props.children}
    </ButtonUI>
  );
});

Button.defaultProps = defaultProps;

export default Button;
