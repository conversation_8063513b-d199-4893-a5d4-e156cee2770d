/* eslint-disable @typescript-eslint/no-explicit-any */
import React from "react";
import { Layout, Table, Text, FormControl } from "unmatched/components";
import appUrls from "unmatched/utils/urls/app-urls";
import { useTable } from "unmatched/hooks";
// import useFilters from "./filter-hook";
// import JSON_DATA from "./people-meta";
// import PeopleFilters from "./PeopleFilters";
import TableContainer from "./TableContainer";
// import { getCategoryAnalyticsFromId } from "../../analytics-api";
import { Link } from "react-router";
import { getCategoryRankingFact } from "../analytics-api";
import util from "unmatched/utils";
import AnalyticsFilters from "../Shared/AnalyticsFilters/AnalyticsFilters";
import useAnalyticsFilters from "../Shared/AnalyticsFilters/hook";
// import useToastr from "unmatched/modules/toastr/hook";
// import PropTypes from 'prop-types'

interface User {
  key: string;
  overallAverage: string;
  selfAverage: string;
  categoryAverage: string;
  frequency: string;
  target: {
    id: string;
    empId: string;
    firstName: string;
    lastName: string;
    email: string;
    metadata: any;
  };
  index: {
    id: string;
    title: string;
  };
}

// interface Request {
//   page?: number;
//   sort?: string;
// }

const getSurveys = (_filters: any) => {
  const hasGroups = _filters.selected.groups.length;
  return {
    groups: _filters.selected.groups,
    surveys: hasGroups ? [] : _filters.selected.surveys,
    type: hasGroups ? ["surveyindex360"] : _filters.selected.types || [],
  };
};

const Categories = (props: any) => {
  const { id } = props;
  const tableMeta = useTable({
    page: 1,
    size: 10,
    totalPages: 10,
  });
  const [users, setUsers] = React.useState<Array<User>>([]);
  // const [categories, setCategories] = React.useState<Array<any>>([]);

  const [columns, setColumns]: any = React.useState({
    emp_id: { label: "Emp ID", sortValue: "", hasSort: true, order: 1 },
    name: {
      label: "Name",
      sortValue: "",
      hasSort: true,
      order: 2,
    },
  });

  const [userColumns, setUserColumns] = React.useState({});

  const [categoryColumns, setCategoryColumns] = React.useState({});

  const filters = useAnalyticsFilters();

  // const toastr = useToastr();

  React.useEffect(() => {
    onTableLoad();
    // getCategoryAnalytics({});

    //eslint-disable-next-line
  }, []);

  const onTableLoad = (year?: any) => {
    filters
      .getFilters({
        year,
      })
      .then(
        (_filters: any) => {
          const [selectedSurvey] = _filters.surveys || [];
          getCategoryAnalytics(
            {},
            {
              ..._filters,
              surveys: _filters.surveys.filter(
                (item: any) => selectedSurvey?.type === item.type
              ),
              selected: {
                ..._filters.selected,
                surveys:
                  selectedSurvey && selectedSurvey.id
                    ? [selectedSurvey.id]
                    : [],
                types: selectedSurvey ? [selectedSurvey.type] : [],
                // groups: params?.groups || _filters.selected.groups,
              },
            }
          );
        },
        (err: any) => {
          console.log(err);
          tableMeta.setLoading(false);
        }
      );
  };

  // const getCategoryKey = (sort: string, _data: any, _selected: any) => {
  //   console.log(_data, _selected)
  //   if (_selected.types.includes("surveyindex360group")) {
  //     let items: any = [];
  //     util.lib.toPairs(_data.categories).forEach(([, value]: any) => {
  //       items = [...items, ...value];
  //     });
  //     return items.find((_item: any) => {
  //       return sort === _item.ordering;
  //     }).id;
  //   }
  //   return sort.split("_")[1];
  // }

  const getCategoryAnalytics = async (
    { page, sort, search }: any,
    _filters?: any
  ) => {
    try {
      if (!_filters.isDownload) {
        tableMeta.setLoading(true);
      }
      const peopleAnalytics = await getCategoryRankingFact(
        {
          index_id: id,
          size: 10,
          search: search || tableMeta.search,
          page: page ?? 1,
          sort: sort || tableMeta.sort || null,
          year: _filters.selected.years,
          ...getSurveys(_filters),
          isDownload: _filters.isDownload,
          // type: _filters.selected.types || ["upward"],
          // surveys: _filters.selected.surveys,
        },
        _filters.meta
      );
      if (_filters.isDownload) return;
      filters.setFilters(_filters);
      tableMeta.updatePagination({
        page,
        totalPages: peopleAnalytics.data.totalPages,
        totalItems: peopleAnalytics.data.totalElements,
      });
      tableMeta.setSort(sort || tableMeta.sort);
      tableMeta.setLoading(false);
      // if (sort?.includes("category")) {
      //   const key = getCategoryKey(sort, peopleAnalytics.data, _filters.selected);
      //   const sortType: any = sort.includes("-") ? "asc" : "desc";
      //   setUsers(
      //     key
      //       ? util.lib.orderBy(peopleAnalytics.data.results, [key], [sortType])
      //       : peopleAnalytics.data.results
      //   );
      // } else {
      // }
      setUsers(peopleAnalytics.data.results);
      // setYear(peopleAnalytics.year);
      // setYears(peopleAnalytics.years);
      // setSurvey(peopleAnalytics.survey);
      // setSurveys(peopleAnalytics.surveys);
      // setCategories(peopleAnalytics.data.categories);

      // _columns: any
      setCategoryColumns(() => {
        let output = {};
        peopleAnalytics.categoryColumns.forEach((item: any) => {
          output = {
            ...output,
            [item.key]: {
              label: item.label,
              hasSort: true,
              isDynamic: true,
              type: "category",
              categoryKey: item.categoryKey,
            },
          };
        });
        return {
          // ..._columns,
          ...output,
        };
      });
      setUserColumns((_columns: any) => {
        let output = {};
        peopleAnalytics.userColumns.forEach((item: any) => {
          output = {
            ...output,
            [item.key]: {
              label: item.label,
              hasSort: true,
              isDynamic: true,
              type: "meta",
            },
          };
        });
        return {
          ..._columns,
          ...output,
        };
      });
      return true;
    } catch (e) {
      console.log(e);
      tableMeta.setLoading(false);
    }
  };

  const onSearch = (search: any) => {
    getCategoryAnalytics({ search }, filters);
  };

  const getSearchInput = () => {
    return (
      <FormControl.Search
        placeholder="Search for name, id"
        size="md"
        className="bg-white w-100"
        value={tableMeta.search}
        onChange={(evt: any) => tableMeta.setSearch(evt.target.value)}
        onSearch={onSearch}
      />
    );
  };

  const getFiltersTemplate = () => {
    return (
      <>
        <Layout.Row>
          <Layout.Col>
            <AnalyticsFilters
              filters={filters}
              hideGroups
              config={{
                surveys: {
                  multiple: false,
                },
                layout: {
                  year: {
                    xl: 2,
                    md: 4,
                    sm: 6,
                  },
                  type: {
                    xl: 3,
                    md: 4,
                    sm: 6,
                  },
                  survey: {
                    xl: 3,
                    md: 4,
                    sm: 6,
                  },
                  group: {
                    xl: 3,
                    md: 4,
                    sm: 6,
                  },
                },
              }}
              onYearChange={(item: any) => {
                onTableLoad(item.key);
              }}
              onTypeChange={(item: any) => {
                const surveys =
                  filters.surveysData.filter((_survey: any) => {
                    if (
                      _survey.type === "surveyindex360group" &&
                      item.key === "surveyindex360"
                    )
                      return true;
                    return _survey.type === item.key;
                  }) || [];
                const [selectedSurvey]: any = surveys || {};
                const groups: any = selectedSurvey.rater_groups?.map(
                  (group: any) => ({
                    id: group.id,
                    key: group.id,
                    title: group.title,
                    groupId: selectedSurvey.id,
                    groupKey: group["rater_group"],
                  })
                );
                getCategoryAnalytics(
                  {},
                  {
                    ...filters,
                    groups,
                    selected: {
                      ...filters.selected,
                      types: [
                        item.key === "surveyindex360"
                          ? "surveyindex360group"
                          : item.key,
                      ],
                      surveys:
                        selectedSurvey && selectedSurvey.id
                          ? [selectedSurvey.id]
                          : [],
                      groups: [],
                    },
                  }
                );
              }}
              onSurveyChange={(items: any) => {
                const selectedSurvey: any =
                  filters.surveys.find((item: any) =>
                    items.includes(item.id)
                  ) || {};
                const groups: any = selectedSurvey.rater_groups?.map(
                  (group: any) => ({
                    id: group.id,
                    key: group.id,
                    title: group.title,
                    groupId: selectedSurvey.id,
                    groupKey: group["rater_group"],
                  })
                );
                getCategoryAnalytics(
                  {},
                  {
                    ...filters,
                    groups,
                    selected: {
                      ...filters.selected,
                      surveys: items,
                      groups: [],
                    },
                  }
                );
              }}
              onGroupChange={(items: any) => {
                getCategoryAnalytics(
                  {},
                  {
                    ...filters,
                    selected: {
                      ...filters.selected,
                      groups: util.lib.compact(items),
                    },
                  }
                );
              }}
            />
          </Layout.Col>
        </Layout.Row>
        <Layout.Row>
          <Layout.Col xl={8} md={8} sm={8} xs={0} className="mt-1"></Layout.Col>
          <Layout.Col
            style={{ display: "flex", justifyContent: "end" }}
            xl={4}
            md={4}
            sm={4}
            xs={12}
            className="mt-1"
          >
            {getSearchInput()}
          </Layout.Col>
        </Layout.Row>
      </>
    );
  };

  const getColumnsData = () => {
    const columnsArray = tableMeta.getColumnsArray(columns);
    // const [userInfo, ] = tableMeta.splitColumns(columnsArray);
    const output = [
      ...columnsArray,
      ...tableMeta.getColumnsArray(userColumns),
      ...tableMeta.filterColumns(tableMeta.getColumnsArray(categoryColumns)),
    ];
    return output;
  };

  const getRowsTemplate = () => {
    return users.map((item: any, index: number) => {
      const isEven = index % 2 === 0 || index === 0;
      return (
        <Table.Row even={!isEven} key={item.empId}>
          <Table.Data>
            <Text.P1>{item.empId}</Text.P1>
          </Table.Data>
          <Table.Data>
            <Link to={appUrls.admin.analytics.people.getAnalyticsUrl(item.id)}>
              <Text.P1>{item.name}</Text.P1>
            </Link>
          </Table.Data>
          {tableMeta.getColumnsArray(userColumns).map(({ key }: any) => {
            return (
              <Table.Data key={key}>
                <Text.P1>
                  {item[key] || (
                    <span className="text-muted">
                      <i>Unavailable</i>
                    </span>
                  )}
                </Text.P1>
              </Table.Data>
            );
          })}
          {tableMeta
            .filterColumns(tableMeta.getColumnsArray(categoryColumns))
            .map(({ key }: any) => {
              return (
                <Table.Data key={key}>
                  <Text.P1>
                    {item[key] || (
                      <span className="text-muted">
                        <i>Unavailable</i>
                      </span>
                    )}
                  </Text.P1>
                </Table.Data>
              );
            })}
        </Table.Row>
      );
    });
  };

  const onPageSelect = async (page: number) => {
    tableMeta.updatePagination({ page });
    await getCategoryAnalytics({ page }, filters);
  };

  if (!users?.length) return null;

  return (
    <TableContainer
      title={"Ranking - By Category Average"}
      filters={getFiltersTemplate()}
      download={() =>
        getCategoryAnalytics({}, { isDownload: true, ...filters })
      }
    >
      <Table
        tableClass="bg-white"
        columns={getColumnsData()}
        isLoading={tableMeta.isLoading}
        rows={users}
        customRows
        render={() => getRowsTemplate()}
        onSort={(item: any) => {
          getCategoryAnalytics(
            {
              // page: tableMeta.page || 1,
              sort: util.label.getSortingLabel(
                item.type === "category"
                  ? item.categoryKey || `category_${item.key}`
                  : item.key,
                item.sortValue
              ),
            },
            filters
          ).then(() => {
            if (item.isDynamic) {
              if (item.type === "meta") {
                setUserColumns((_columns: any) =>
                  tableMeta.resetColumns(_columns, item)
                );
              } else {
                setCategoryColumns((_columns: any) =>
                  tableMeta.resetColumns(_columns, item)
                );
              }
            } else {
              setColumns((_columns: any) =>
                tableMeta.resetColumns(_columns, item)
              );
            }
          });
        }}
        onNavigate={({ nextArrow, prevArrow }: any) => {
          const arr = tableMeta.getColumnsArray(categoryColumns);
          if (nextArrow) {
            const index =
              arr.findIndex((item: any) => item.key === nextArrow) + 1;
            tableMeta.setRenderIndex(
              index >= arr.length ? arr.length - 1 : index
            );
          } else if (prevArrow) {
            const index =
              arr.findIndex((item: any) => item.key === prevArrow) - 1;
            tableMeta.setRenderIndex(index < 0 ? 0 : index);
          }
        }}
        hasPagination
        activePage={tableMeta.page}
        pages={tableMeta.totalPages}
        onPageSelect={onPageSelect}
        notFoundMsg={util.noSearchRecordsFoundMsg}
        size={tableMeta.size}
        totalItems={tableMeta.totalItems}
      />
    </TableContainer>
  );
};

export default Categories;
