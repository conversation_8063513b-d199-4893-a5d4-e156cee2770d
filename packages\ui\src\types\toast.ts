export type ToastVariant = "success" | "danger" | "warning" | "info";

export interface ToastConfig {
  show?: boolean;
  title?: string;
  variant: ToastVariant;
  content: string;
  delay?: number;
}

export interface SuccessConfig {
  title?: string;
  content?: string;
}

export interface ErrorType {
  msg?: string;
  message?: string;
  statusCode?: number;
}

export interface ToastItem extends ToastConfig {
  id: string;
  timestamp: number;
}

export interface UseToastrReturn {
  toastrs: ToastItem[];
  resetToast: (index: number) => void;
  showToast: (config: ToastConfig) => void;
  onError: (err: ErrorType | string) => void;
  onSucces: (config: SuccessConfig) => void;
  errorToast: (content?: string) => void;
  warningToast: (content?: string) => void;
}

export interface ToastProps {
  variant?: ToastVariant;
  title?: string;
  content?: string;
  delay?: number;
  show?: boolean;
  topRight?: boolean;
  onClose?: () => void;
}
