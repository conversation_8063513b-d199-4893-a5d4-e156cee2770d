import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@repo/ui/components/form";
import { Input } from "@repo/ui/components/input";
// import { Text } from "@repo/ui/components/Text";
import { Eye, EyeOff } from "lucide-react";
import { useRef, useState } from "react";
// import {
//   FormControl,
//   FormGroup,
//   Layout,
//   Overlay,
//   Tooltip,
//   Text,
//   Icon,
//   div,
// } from "@unmatched/components";
// import { isFieldInvalid, isFieldValid } from "unmatched/utils/formik";
// import styled from "styled-components";
// import util from "@unmatched/utils";

// const { isFieldInvalid, isFieldValid } = util.formik;

// export const StyledIcon = styled(Icon)`
//   position: absolute;
//   right: 44px;
//   margin-top: -23px;
// `;

// const ValidationTooltip = () => { };

// const getValidationLine = (valid: boolean, text: string) => {
//   const { className, icon } = !valid
//     ? {
//         icon: "far fa-times",
//         className: "text-danger",
//       }
//     : {
//         icon: "far fa-check",
//         className: "text-success",
//       };
//   return (
//     <div className="flex">
//       <div>
//         <Icon icon={icon} className={className} />
//       </div>
//       <Text.P2 className="pl-2">{text}</Text.P2>
//     </div>
//   );
// };

export default function PasswordForm(props: {
  form: any;
  // onPasswordChange: Function;
  // tooltip: any;
  // colXL?: number;
}) {
  //  onPasswordChange, tooltip
  const { form } = props;
  // const passwordRef = useRef();
  const [showPass, setShowPass] = useState(false);

  // const isMatch = () => {
  //   const { values } = formik;
  //   return values.password === values.confirmPassword;
  // };

  // const getConfirmPasswordFeedback = () => {
  //   const { touched, errors } = formik;
  //   if (!touched.confirmPassword) return "";
  //   if (errors.confirmPassword) return errors.confirmPassword;
  //   if (!isMatch()) return "Passwords do not match";
  // };
  // const PassOrText = showPass ? FormControl.Text : FormControl.Password;
  return (
    <>
      <div className="">
        {/* <div className="xl:col-span-5"> */}
        {/* <FormGroup>
            <FormGroup.Label>Password</FormGroup.Label>
            <PassOrText
              ref={passwordRef}
              name="password"
              isInvalid={isFieldInvalid(formik, "password")}
              isValid={isFieldValid(formik, "password")}
              onBlur={formik.handleBlur}
              onChange={(event: any) => {
                if (onPasswordChange) {
                  onPasswordChange(event);
                } else {
                  formik.handleChange(event);
                }
              }}
              value={formik.values?.password || ""}
              placeholder="Password"
            /> */}
        {/* <div onClick={() => setShowPass((s) => !s)}>
              <StyledIcon icon="fas fa-eye" />
            </div> */}
        {/* <Overlay
              target={() => passwordRef.current || null}
              show={isFieldInvalid(formik, "password")}
              placement="right"
            >
              {(props) => (
                <Tooltip id="password" {...props}>
                  <div className="p-2 text-left text-white">
                    <Text.P2 className="pb-1">Password needs to have:</Text.P2>
                    {getValidationLine(tooltip.min, "At least 8 characters")}
                    {getValidationLine(tooltip.alphabet, "At least 1 letter")}
                    {getValidationLine(tooltip.number, "At least 1 number")}
                    {tooltip.same !== undefined &&
                      getValidationLine(
                        tooltip.same,
                        "Cannot be same as old password"
                      )}
                  </div>
                </Tooltip>
              )}
            </Overlay> */}
        {/* </FormGroup> */}
        <FormField
          control={form.control}
          name="password"
          render={({ field }) => (
            <FormItem className="relative">
              <FormLabel>Password</FormLabel>
              <FormControl>
                <Input {...field} type={showPass ? "text" : "password"} />
              </FormControl>
              <div onClick={() => setShowPass((s) => !s)} className="absolute right-5 top-8">
                {showPass ? <Eye  className="size-4"/> : <EyeOff  className="size-4"/>}
              </div>
              <FormMessage />
            </FormItem>
          )}
        />
        {/* </div> */}
      </div>
      <div className="py-3 grid grid-col-12">
        {/* <div className="xl:col-span-5"> */}
        {/* <FormGroup>
            <FormGroup.Label>Confirm password</FormGroup.Label>
            <Input
              name="confirmPassword"
              type={showPass ? "text" : "password"}
              // isInvalid={isFieldInvalid(formik, "confirmPassword")}
              // isValid={isFieldValid(formik, "confirmPassword")}
              // onBlur={formik.handleBlur}
              // onChange={formik.handleChange}
              placeholder="Re-enter password"
            />
            <FormGroup.InValidFeedback text={getConfirmPasswordFeedback()} />
          </FormGroup> */}
        <FormField
          control={form.control}
          name="confirmPassword"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Confirm password</FormLabel>
              <FormControl>
                <Input placeholder="Re-enter password" {...field} type="password"/>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>
      {/* </div> */}
    </>
  );
}
