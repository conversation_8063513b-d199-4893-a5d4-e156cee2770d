import React, { useMemo } from "react";
// import Text from "../Text/Text";
// import InfiniteScroll from "../InfiniteScroll";
// import Icon from "../Icon";
// import Button from "../Button/Button";
// import Pagination from "../Pagination/Pagination";
import Div from "../Div";
import Filter from "./Filter/Filter";
import FilterMultiSelect from "./FilterMultiSelect/FilterMutiSelect";
import CompareFilter from "./CompareFilter/CompareFilter";
import styled from "styled-components";
import NotFound from "./NotFound";
// import style from "./Table.module.css";

const InfoH = styled(Div)`
  font-family: "Inter";
  font-style: normal;
  font-weight: 500;
  font-size: 14px;
  line-height: 17px;
  color: #000000;
  margin-top: 20px;
`;

const InfoP = styled(Div)`
  font-family: "Inter";
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  line-height: 17px;
  text-align: center;
  color: #737373;
  margin-top: 10px;
`;

// import "./Table.scss";
import util from "../../utils";
import TableLoading from "../TableLoading/TableLoading";
// import Button from "../Button/Button";
import {
  ChevronDownIcon,
  ChevronLeftIcon,
  ChevronRightIcon,
  ChevronsUpDownIcon,
  ChevronUpIcon,
  Loader2,
} from "lucide-react";
import { Text } from "@repo/ui/components/Text";
import Pagination from "../Pagination/Pagination";
import { Button } from "@repo/ui";

type Variant = "light" | "dark" | "info" | "primary" | "danger" | "success";
type HeadVariant = "light" | "dark";
type TableType = "striped" | "bordered" | "borderless" | "hover";

interface TableProps {
  columns: Array<any>;
  rows: Array<any>;
  variant?: Variant;
  headVariant?: HeadVariant;
  type?: TableType;
  responsive?: boolean;
  customRows?: boolean;
  hasInfiniteScroll?: boolean;
  hasPagination?: boolean;
  pages?: number;
  breakPoint?: string;
  containerClass?: string;
  headerClass?: string;
  tableClass?: string;
  bodyClass?: string;
  render?: Function;
  onSort?: Function;
  onNavigate?: Function;
  isLoading?: boolean;
  noDataTemplate?: React.ReactNode;
  loadingContent?: React.ReactNode;
  onInfinite?: Function;
  children?: React.ReactNode;
  onPageSelect?: Function;
  activePage?: number;
  onRowClick?: Function;
  notFoundMsg?: String;
  size?: number;
  totalItems?: number;
  // More functionalities related to Table component can be implemented, as of now iam ignoring these functionalities.
  // draggable?: boolean;
  // onDrag?: Function;
  // resizable?: boolean;
  // onResize?: Function;
  // columnsMeta?: any;
  // fixedHeader?: boolean;
  // fixedFooter?: boolean;
  // footerItems?: Array<any>;
}

interface TableDataProps {
  children?: React.ReactNode;
  colSpan?: number;
  rowSpan?: number;
  dataClass?: string;
  style?: any;
  width?: string;
  centered?: boolean;
}

interface TableRowProps {
  children: React.ReactNode;
  className?: string;
  style?: any;
  selected?: boolean;
  even?: boolean;
  onClick?: Function;
  clickable?: boolean;
}

// Helper functions
const sorts = [
  util.enums.Sort.None,
  util.enums.Sort.Descending,
  util.enums.Sort.Ascending,
];

const getSortKey = (key: any) => {
  const index = sorts.indexOf(key);
  if (!index) {
    return util.enums.Sort.Descending;
  } else if (index === 1) {
    return util.enums.Sort.Ascending;
  }
  return util.enums.Sort.None;
};

const getContainerClass = (props: TableProps) => {
  const responsiveClass = props.responsive
    ? `table-responsive${props.breakPoint}`
    : "";
  return `Custom-Table ${responsiveClass} ${props.containerClass}`;
};

const getTableClass = (props: TableProps) => {
  const { variant, type, tableClass } = props;
  const typeClass = `table-${type}`;
  const variantClass = variant ? `table-${variant}` : "";
  return `table ${typeClass} table-bordered ${variantClass} ${tableClass}`;
};

const getHeaderClass = (props: TableProps) => {
  const headerClass = "table-head";
  return `${headerClass} ${props.headerClass}`;
};

const getBodyClass = (props: TableProps) => {
  return props.bodyClass;
};

const getSortIcon = (item: any, onSort?: Function) => {
  const { sortValue } = item;
  // let icon = "fad fa-sort";
  let icon = <ChevronsUpDownIcon className="size-4 " />
  if (sortValue === "asc") {
    // icon = "fad fa-sort-up";
    icon = <ChevronUpIcon className="size-4 " />
  } else if (sortValue === "dsc") {
    // icon = "fad fa-sort-down";
    icon = <ChevronDownIcon className="size-4 " />
  }
  return (
    <Button
      // className="!p-0"
      size="sm"
      variant="link"
      onClick={() => {
        return onSort
          ? onSort({
              ...item,
              sortValue: getSortKey(item.sortValue),
            })
          : "";
      }}
    >
      {/* <Icon icon={icon} className="text-muted fs-14" /> */}
      {icon}
    </Button>
  );
};

// Template functions

const getColumnTemplate = (item: any, props: TableProps) => {
  const { onSort } = props;
  if (item.renderItem) {
    return item.renderItem(item);
  }
  return (
    <Div className="flex">
      {item.prevArrow && (
        <Div>
          <Button
            variant="ghost"
            onClick={() => props.onNavigate && props.onNavigate(item)}
            className="py-0"
          >
            {/* <Icon icon="fas fa-caret-left" /> */}
            <ChevronLeftIcon className="size-5" />
          </Button>
        </Div>
      )}
      <Text.P1 className="self-center" style={{ fontWeight: 600 }}>
        {item.label}
      </Text.P1>
      {item.nextArrow && (
        <Div className="ml-auto">
          <Button
            className="py-0"
            variant="ghost"
            onClick={() => props.onNavigate && props.onNavigate(item)}
          >
            {/* <Icon icon="fas fa-caret-right" /> */}
            <ChevronRightIcon className="size-5" />
          </Button>
        </Div>
      )}
      {item.hasSort && (
        <Div className="ml-auto cursor-pointer">
          {getSortIcon(item, onSort)}
        </Div>
      )}
    </Div>
  );
};

// Components

const TableData = (props: TableDataProps) => {
  const { centered } = props;
  const dataClass = centered ? `${props.dataClass} text-center` : "";
  return (
    <td
      width={props.width}
      colSpan={props.colSpan || 1}
      rowSpan={props.rowSpan || 1}
      className={dataClass}
    >
      {props.children}
    </td>
  );
};

const TableRow = (props: TableRowProps) => {
  const getRowClass = () => {
    const evenClass = props.even ? "bg-primary/5" : "";
    const selectedClass = props.selected ? "table-selected" : "";
    const clickableClass = props.onClick ? "cursor-pointer" : "";
    return `table-row ${evenClass} ${selectedClass} ${clickableClass}`;
  };
  return (
    <tr
      className={getRowClass()}
      style={props.style}
      onClick={() => (props.onClick ? props.onClick() : "")}
    >
      {props.children}
    </tr>
  );
};

const Table: any = (props: TableProps) => {
  // Not using useMemo for all class utils because there are no heavy computations involved
  const containerClass = getContainerClass(props);
  const tableClass = getTableClass(props);
  const headerClass = getHeaderClass(props);
  const bodyClass = getBodyClass(props);

  const {
    // hasInfiniteScroll,
    hasPagination,
    // onInfinite,
    isLoading,
    loadingContent,
    columns,
    rows,
    render,
    pages,
    onPageSelect,
    activePage,
    customRows,
    size,
    totalItems,
  } = props;

  // const columnsLength = columns.length;

  const loadingTemplate = useMemo(() => {
    // if (hasInfiniteScroll && onInfinite) {
    //   return (
    //     <tr className="select-none">
    //       <td colSpan={columnsLength}>
    //         <InfiniteScroll
    //           onChange={(inView: boolean) => {
    //             if (inView) onInfinite(inView);
    //           }}
    //         >
    //           {loadingContent}
    //         </InfiniteScroll>
    //       </td>
    //     </tr>
    //   );
    // }
    return loadingContent;
  }, [
    //hasInfiniteScroll, onInfinite,
    loadingContent,
    //columnsLength
  ]);

  const getLoadingTemplate = () => {
    return (
      <TableRow>
        <TableData colSpan={columns.length}>
          {loadingTemplate || (
            // <Icon icon="far fa-spinner" variant="primary" spin />
            <Loader2 className="size-5 text-primary animate-spin" />
          )}
        </TableData>
      </TableRow>
    );
  };

  const getNoDataFoundTemplate = () => {
    return (
      <TableRow style={{ height: 350 }}>
        <TableData centered colSpan={columns.length}>
          <NotFound style={{ marginTop: 62 }} />
          <InfoH>No data found</InfoH>
          <InfoP>{props.notFoundMsg || ""}</InfoP>
        </TableData>
      </TableRow>
    );
  };

  const getTableRows = () => {
    if (isLoading) {
      return getLoadingTemplate();
    } else if (!rows.length) {
      return getNoDataFoundTemplate();
    } else if (customRows) {
      return <>{render && render()}</>;
    }
    // function id () : any{
    //   return Math.floor(Math.random() * Math.floor(Math.random() * Date.now()))
    // }
    return rows.map((item: any, index: number) => {
      const isEven = index % 2 === 0 || index === 0;
      return (
        <TableRow
          onClick={() => (props.onRowClick ? props.onRowClick(item) : "")}
          key={`${Math.floor(
            Math.random() * Math.floor(Math.random() * Date.now())
          )}`}
          even={!isEven}
        >
          {render && render(item, index)}
        </TableRow>
      );
    });
  };

  const getPagintationTemplate = () => {
    if (!isLoading && hasPagination && pages && pages > 1) {
      // if (!isLoading && hasPagination && pages) {
      return (
        <Div id="pagination" className="text-right">
          <Pagination
            pages={pages}
            active={activePage}
            onSelect={onPageSelect}
            pageSize={size ?? 10}
            totalItems={totalItems}
          />
        </Div>
      );
      // }
    }
  };

  const getTableTemplate = () => {
    if (isLoading) {
      return (
        <TableLoading
          pages={pages}
          active={activePage}
          size={size}
          totalItems={totalItems}
        />
      );
    }
    // const id: any = () => {
    //   return
    // }
    return (
      <Div className={containerClass}>
        <table className={tableClass}>
          <thead className={headerClass}>
            <tr className="select-none">
              {columns.map((item: any) => {
                return (
                  <th
                    className="py-2"
                    key={`${Math.floor(
                      Math.random() * Math.floor(Math.random() * Date.now())
                    )}_${item.id ?? ""}`}
                    data-key={`${Math.floor(
                      Math.random() * Math.floor(Math.random() * Date.now())
                    )}_${item.id ?? ""}`}
                  >
                    {getColumnTemplate(item, props)}
                  </th>
                );
              })}
            </tr>
          </thead>
          <tbody className={bodyClass}>
            {getTableRows()}
            {loadingTemplate}
          </tbody>
        </table>
      </Div>
    );
  };

  return (
    <>
      {getTableTemplate()}
      {getPagintationTemplate()}
    </>
  );
};

Table.defaultProps = {
  columns: [],
  rows: [],
  pages: 0,
  responsive: true,
  breakPoint: "",
  type: "bordered",
  variant: "",
  headVariant: "light",
  containerClass: "text-left pb-1",
  tableClass: "",
  headerClass: "",
  bodyClass: "",
};

Table.Data = TableData;

Table.Row = TableRow;

Table.Filter = Filter;

Table.FilterMultiSelect = FilterMultiSelect;

Table.CompareFilter = CompareFilter;

export default Table;
