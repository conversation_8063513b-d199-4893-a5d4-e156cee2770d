/* eslint-disable @typescript-eslint/no-explicit-any */
import { DownloadIcon } from "lucide-react";
import { Button, Div, Text } from "unmatched/components";

const TableContainer = (props: any) => {
  return (
    <Div className="bg-light py-3 my-4 border">
      <Div className="p-3">
        <Div className="row mb-3">
          <Div className="col-8">
            <Text.H3 className="pb-3">{props.title}</Text.H3>
          </Div>
          <Div className="col-4 flex justify-end">
            {props.download ? (
              <Button variant="outline" onClick={props.download}>
               Download XLSX <DownloadIcon className="ml-2 inline-block size-5" /> {/* <Icon icon="fas fa-file-download ml-2" />} */}
              </Button>
            ) : null}
          </Div>
        </Div>

        {props.filters}
      </Div>
      {props.children}
    </Div>
  );
};

export default TableContainer;
