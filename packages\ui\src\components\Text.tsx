import React from "react";
import { cx } from "class-variance-authority";

const H1 = React.forwardRef<HTMLHeadingElement, React.ComponentProps<"h1">>(
  ({ className, children, ...props }, ref) => (
    <h1 className={cx("text-2xl font-semibold", className)} ref={ref} {...props}>
      {children}
    </h1>
  )
);

const H2 = React.forwardRef<HTMLHeadingElement, React.ComponentProps<"h2">>(
  ({ className, children, ...props }, ref) => (
    <h2 className={cx("text-lg font-semibold", className)} ref={ref} {...props}>
      {children}
    </h2>
  )
);


const H3 = React.forwardRef<HTMLHeadingElement, React.ComponentProps<"h2">>(
  ({ className, children, ...props }, ref) => (
    <h2 className={cx("text-base font-semibold", className)} ref={ref} {...props}>
      {children}
    </h2>
  )
);

const H4 = React.forwardRef<HTMLHeadingElement, React.ComponentProps<"h2">>(
  ({ className, children, ...props }, ref) => (
    <h2 className={cx("text-xs font-semibold", className)} ref={ref} {...props}>
      {children}
    </h2>
  )
);



const P1 = React.forwardRef<HTMLParagraphElement, React.ComponentProps<"p">>(
  ({ className, children, ...props }, ref) => (
    <p className={cx("text-sm font-normal", className)} ref={ref} {...props}>
      {children}
    </p>
  )
);

const P2 = React.forwardRef<HTMLParagraphElement, React.ComponentProps<"p">>(
  ({ className, children, ...props }, ref) => (
    <p className={cx("text-xs font-normal", className)} ref={ref} {...props}>
      {children}
    </p>
  )
);

const InputLabel  = React.forwardRef<HTMLParagraphElement, React.ComponentProps<"p">>(
  ({ className, children, ...props }, ref) => (
    <p className={cx("text-[0.875rem] leading-[1.059rem] font-normal text-[#6d6d6d]", className)} ref={ref} {...props}>{children}</p>
  )
)


H1.displayName = "H1";
H2.displayName = "H2";
H3.displayName = "H3";
H4.displayName = "H4";
P1.displayName = "P1";
P2.displayName = "P2";
InputLabel.displayName = "InputLabel";


export const Text = {
  H1,
  H2,
  H3,
  H4,
  P1,
  P2,
  InputLabel,
};
