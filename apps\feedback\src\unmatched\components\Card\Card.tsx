import React from "react";
import { cn } from "../../../lib/utils";
import Div from "../Div";
import { Text } from "../../../unmatched/components";

interface CardProps {
  children: React.ReactNode;
  className?: string;
  noShadow?: boolean;
  onClick?: Function;
  style?: any;
}

const Header = (props: CardProps) => {
  const { children, className, style } = props;
  // Convert Bootstrap bg-light to Tailwind equivalent
  const containerClass = cn("bg-gray-50", className);
  return (
    <Div style={style} className={containerClass}>
      {children}
    </Div>
  );
};

const Title = (props: CardProps) => {
  const { children } = props;
  // Convert Bootstrap text-primary pb-2 to Tailwind equivalent
  return <Text.H3 className="text-blue-600 pb-2">{children}</Text.H3>;
};

const Body = (props: CardProps) => {
  const { children, className } = props;
  // Convert Bootstrap responsive padding classes to Tailwind equivalent
  const bodyClass = cn(
    "p-2 pb-3",
    "xl:p-4 lg:p-4 md:p-3 sm:p-3 xs:p-3",
    className
  );
  return (
    <div className={bodyClass}>
      {children}
    </div>
  );
};

const Footer = (props: CardProps) => {
  const { children, className } = props;
  // Convert Bootstrap text-right py-3 to Tailwind equivalent
  const defaultClass = "text-right py-3";
  const footerClass = cn(defaultClass, className);
  return <div className={footerClass}>{children}</div>;
};

const Card = (props: CardProps) => {
  const { className, children, onClick, noShadow, style } = props;
  
  // Convert Bootstrap card styling to Tailwind equivalent
  // Original: box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.14); border-radius: 2px;
  const shadowClass = noShadow ? "shadow-none" : "shadow-lg";
  const defaultClass = cn(
    "bg-white border border-gray-200 rounded-sm", // rounded-sm for 2px border-radius
    shadowClass
  );
  const cardClass = cn(defaultClass, className);
  
  return (
    <div
      onClick={() => {
        if (onClick) onClick();
      }}
      className={cardClass}
      style={style}
    >
      {children}
    </div>
  );
};

Card.defaultProps = {
  className: "",
};

Title.defaultProps = {
  className: "",
};

Body.defaultProps = {
  className: "",
};

Footer.defaultProps = {
  className: "",
};

Card.Title = Title;
Card.Body = Body;
Card.Footer = Footer;
Card.Header = Header;

export default Card;
