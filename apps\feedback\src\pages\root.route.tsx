import useSession from "@/unmatched/modules/session/hook";
import { useEffect } from "react";
import { useNavigate } from "react-router";
import LayoutDemo from "./LayoutDemo";
import ModalDemo from "./ModalDemo";
import ToastDemo from "./ToastDemo";
import TooltipDemo from "./TooltipDemo";

const RootPath = [
  {
    path: "/",
    element: <Root />,
  },
  {
    path: "/layout-demo",
    element: <LayoutDemo />,
  },
  {
    path: "/modal-demo",
    element: <ModalDemo />,
  },
  {
    path: "/toast-demo",
    element: <ToastDemo />,
  },
  {
    path: "/tooltip-demo",
    element: <TooltipDemo />,
  },
];

function Root() {
  const { user } = useSession();
  const navigate = useNavigate();
  useEffect(() => {

    if (user.role === "ADMIN") {
      navigate("/admin");
    } else {
      navigate("/user");
    }
    //eslint-disable-next-line
  }, [user]);

  return <>Loading...</>;
}
export default RootPath;
