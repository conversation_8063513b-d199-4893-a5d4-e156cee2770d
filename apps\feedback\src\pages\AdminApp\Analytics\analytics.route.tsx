import { Outlet } from "react-router";
import ADMIN_URLS from "@/app.routes";
import RankingList from "./RankingList/RankingList";

const AnalyticsRoute = [
  {
    path: ADMIN_URLS().ANALYTICS.default,
    element: <Outlet />,
    children: [
      {
        path: ADMIN_URLS().ANALYTICS.default,
        element: <>Analytics</>,
      },
      {
        path: ADMIN_URLS().ANALYTICS.geRankingListUrl(),
        element: <RankingList />,
      },
      {
        path: ADMIN_URLS().ANALYTICS.people.default,
        element: <>People Analytics</>,
      },
      {
        path: ADMIN_URLS().ANALYTICS.getAggregateUrl(),
        element: <>Aggregate Analytics</>,
      },
    ],
  },
];
export default AnalyticsRoute;
