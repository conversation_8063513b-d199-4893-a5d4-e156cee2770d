import { useState, useCallback } from "react";
import { toast } from "sonner";
import { CheckCircle, XCircle, AlertCircle, Info, X } from "lucide-react";
import type { 
  ToastConfig, 
  SuccessConfig, 
  ErrorType, 
  UseToastrReturn, 
  ToastItem,
  ToastVariant 
} from "../types/toast";

// Icon mapping for different variants
const variantIcons = {
  success: CheckCircle,
  danger: XCircle,
  warning: AlertCircle,
  info: Info,
} as const;

// Color mapping for different variants
const variantColors = {
  success: "#36B37E",
  danger: "#F34115", 
  warning: "#FDBC3D",
  info: "#518cff",
} as const;

export default function useToastr(): UseToastrReturn {
  const [toastrs, setToastrs] = useState<ToastItem[]>([]);

  const showToast = useCallback((config: ToastConfig) => {
    const { variant, title, content, delay = 4000 } = config;
    
    // Check for duplicate messages (matching legacy behavior)
    const hasMessage = toastrs.find(
      (item) => item.title === title && item.content === content
    );
    
    if (hasMessage) {
      return;
    }

    const toastId = Math.random().toString(36).substr(2, 9);
    const toastItem: ToastItem = {
      ...config,
      id: toastId,
      timestamp: Date.now(),
    };

    // Add to toastrs array for compatibility
    setToastrs(prev => [...prev, toastItem]);

    const IconComponent = variantIcons[variant];
    const color = variantColors[variant];

    // Create custom toast content with icon and styling
    const toastContent = (
      <div className="flex items-start gap-3 w-full">
        <div className="flex-shrink-0 mt-0.5">
          <IconComponent 
            size={20} 
            style={{ color }} 
            className="flex-shrink-0"
          />
        </div>
        <div className="flex-1 min-w-0">
          {title && (
            <div className="font-medium text-sm mb-1 text-foreground">
              {title}
            </div>
          )}
          {content && (
            <div className="text-sm text-muted-foreground break-words">
              {content}
            </div>
          )}
        </div>
      </div>
    );

    // Map variants to Sonner toast types
    const toastOptions = {
      duration: delay,
      id: toastId,
      onDismiss: () => {
        setToastrs(prev => prev.filter(item => item.id !== toastId));
      },
      onAutoClose: () => {
        setToastrs(prev => prev.filter(item => item.id !== toastId));
      },
      style: {
        borderLeft: `6px solid ${color}`,
      },
    };

    switch (variant) {
      case "success":
        toast.success(toastContent, toastOptions);
        break;
      case "danger":
        toast.error(toastContent, toastOptions);
        break;
      case "warning":
        toast.warning(toastContent, toastOptions);
        break;
      case "info":
        toast.info(toastContent, toastOptions);
        break;
      default:
        toast(toastContent, toastOptions);
    }
  }, [toastrs]);

  const resetToast = useCallback((index: number) => {
    setToastrs(prev => {
      const toastToRemove = prev[index];
      if (toastToRemove) {
        toast.dismiss(toastToRemove.id);
      }
      return prev.filter((_, i) => i !== index);
    });
  }, []);

  const errorToast = useCallback((content: string = "Something went wrong.") => {
    if (typeof content !== "string") {
      content = "Something went wrong.";
    }
    showToast({
      show: true,
      title: "Error",
      variant: "danger",
      content,
    });
  }, [showToast]);

  const warningToast = useCallback((content: string = "Something went wrong.") => {
    if (typeof content !== "string") {
      content = "Something went wrong.";
    }
    showToast({
      show: true,
      variant: "warning",
      content,
    });
  }, [showToast]);

  const onError = useCallback((err: ErrorType | string) => {
    let message: string;
    
    if (typeof err === "string") {
      message = err;
    } else {
      message = err.msg || err.message || "Something went wrong.";
    }
    
    if (message === "Canceled Request") return;
    
    if (typeof message !== "string") {
      message = "Something went wrong.";
    }
    
    showToast({
      show: true,
      variant: "danger",
      content: message || "Something went wrong",
    });
  }, [showToast]);

  const onSucces = useCallback((config: SuccessConfig) => {
    showToast({
      show: true,
      variant: "success",
      title: config.title || "Success",
      content: config.content || "",
    });
  }, [showToast]);

  return {
    toastrs,
    resetToast,
    showToast,
    onError,
    onSucces,
    errorToast,
    warningToast,
  };
}
