import React, { useEffect } from "react";
import { toast } from "sonner";
import { CheckCircle, XCircle, AlertCircle, Info, X } from "lucide-react";
import { cn } from "../lib/utils";
import type { ToastProps } from "../types/toast";

// Icon mapping for different variants
const variantIcons = {
  success: CheckCircle,
  danger: XCircle,
  warning: AlertCircle,
  info: Info,
} as const;

// Color mapping for different variants
const variantColors = {
  success: "#36B37E",
  danger: "#F34115", 
  warning: "#FDBC3D",
  info: "#518cff",
} as const;

// Legacy Toast component that wraps Sonner for compatibility
const Toast: React.FC<ToastProps> = ({
  variant = "info",
  title,
  content,
  delay = 4000,
  show = true,
  topRight = true,
  onClose,
}) => {
  useEffect(() => {
    if (!show) return;

    const IconComponent = variantIcons[variant];
    const color = variantColors[variant];

    // Create custom toast content with icon and styling to match legacy design
    const toastContent = (
      <div className="flex items-start gap-3 w-full p-2">
        <div className="flex-shrink-0 mt-0.5">
          <IconComponent 
            size={20} 
            style={{ color }} 
            className="flex-shrink-0"
          />
        </div>
        <div className="flex-1 min-w-0">
          {title && (
            <div className="font-medium text-sm mb-1 text-foreground">
              {title}
            </div>
          )}
          {content && (
            <div className="text-sm text-muted-foreground break-words">
              {content}
            </div>
          )}
        </div>
        {onClose && (
          <button
            onClick={onClose}
            className="flex-shrink-0 ml-2 p-1 hover:bg-gray-100 rounded-sm transition-colors"
            aria-label="Close toast"
          >
            <X size={16} className="text-muted-foreground" />
          </button>
        )}
      </div>
    );

    const toastOptions = {
      duration: delay,
      onDismiss: onClose,
      onAutoClose: onClose,
      style: {
        borderLeft: `6px solid ${color}`,
        padding: 0, // Remove default padding since we handle it in content
      },
      className: cn(
        "min-w-[400px] max-w-[900px]",
        topRight && "toast-top-right"
      ),
    };

    let toastId: string | number;

    // Map variants to Sonner toast types
    switch (variant) {
      case "success":
        toastId = toast.success(toastContent, toastOptions);
        break;
      case "danger":
        toastId = toast.error(toastContent, toastOptions);
        break;
      case "warning":
        toastId = toast.warning(toastContent, toastOptions);
        break;
      case "info":
        toastId = toast.info(toastContent, toastOptions);
        break;
      default:
        toastId = toast(toastContent, toastOptions);
    }

    // Cleanup function to dismiss toast if component unmounts
    return () => {
      if (toastId) {
        toast.dismiss(toastId);
      }
    };
  }, [show, variant, title, content, delay, topRight, onClose]);

  // This component doesn't render anything directly since Sonner handles the rendering
  return null;
};

// Additional components for compatibility with legacy usage patterns
const ToastBody: React.FC<{ children: React.ReactNode; className?: string }> = ({ 
  children, 
  className 
}) => (
  <div className={cn("p-0", className)}>
    {children}
  </div>
);

const ToastHeader: React.FC<{ 
  children: React.ReactNode; 
  className?: string;
  closeButton?: boolean;
  onClose?: () => void;
}> = ({ 
  children, 
  className,
  closeButton = true,
  onClose 
}) => (
  <div className={cn("flex items-center justify-between p-2 border-b", className)}>
    <div>{children}</div>
    {closeButton && onClose && (
      <button
        onClick={onClose}
        className="p-1 hover:bg-gray-100 rounded-sm transition-colors"
        aria-label="Close"
      >
        <X size={16} />
      </button>
    )}
  </div>
);

// Export the main Toast component and sub-components for full compatibility
export default Toast;
export { Toast, ToastBody, ToastHeader };
