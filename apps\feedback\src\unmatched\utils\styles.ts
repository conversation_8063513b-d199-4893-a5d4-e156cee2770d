import _ from "lodash";

const mapper = {
  // Spacers
  p: (val: number) => `p-${val}`,
  m: (val: number) => `m-${val}`,
  py: (val: number) => `py-${val}`,
  my: (val: number) => `my-${val}`,
  px: (val: number) => `px-${val}`,
  mx: (val: number) => `mx-${val}`,
  pr: (val: number) => `pr-${val}`,
  mr: (val: number) => `mr-${val}`,
  pl: (val: number) => `pl-${val}`,
  ml: (val: number) => `ml-${val}`,
  pt: (val: number) => `pt-${val}`,
  mt: (val: number) => `mt-${val}`,
  pb: (val: number) => `pb-${val}`,
  mb: (val: number) => `mb-${val}`,
  // Text
  fs: (val: number) => `fs-${val}`,
  fw: (val: number) => `fs-${val}`,
  textColor: (val: string) => `text-${val}`,
  textTransform: (val: string) => `text-${val}`,
  textAlign: (val: string) => `text-${val}`,
  wordBreak: (val: string) => (val ? "text-break" : ""),
  justifyContent: (val: string) => `justify-content-${val}`,
  alignSelf: (val: string) => `align-self-${val}`,
  // Display utils
  bg: (val: string) => `bg-${val}`,
  img: (val: string) => `img-${val}`,
  vScroll: () => "",
  hScroll: () => "",
  position: (val: any) => `position-${val}`,
  float: (val: any) => `float-${val}`,
  v: (val: any) => (val ? "visible" : "invisible"),
  d: (val: any) => `d-${val}`,
  w: (val: any) => `w-${val}`,
  h: (val: any) => `h-${val}`,
  minHeight: (val: any) => `min-h-${val}`,
  maxHeight: (val: any) => `max-h-${val}`,
  minWidth: (val: any) => `min-w-${val}`,
  maxWidth: (val: any) => `max-w-${val}`,
  // minHeightVh: (val: any) => `max-vh-h-${val}`,
  // maxHeightVh: (val: any) => `max-vh-h-${val}`,
  // minHeightPx: (val: any) => `max-px-h-${val}`,
  // maxHeightPx: (val: any) => `max-px-h-${val}`,
  // minWidthVh: (val: any) => `max-vh-w-${val}`,
  // maxWidthVh: (val: any) => `max-vh-w-${val}`,
  // minWidthPx: (val: any) => `max-px-w-${val}`,
  // maxWidthPx: (val: any) => `max-px-w-${val}`,
};

const getUtilClassName = (data: any) => {
  const keys = _.keys(data);
  return _.reduce(
    keys,
    (className: string, key: any) => {
      const getStyle = _.get(mapper, key);
      const value = _.get(data, key);
      const newClass = getStyle ? getStyle(value) : "";
      return `${className} ${newClass}`;
    },
    ""
  );
};

export default getUtilClassName;
