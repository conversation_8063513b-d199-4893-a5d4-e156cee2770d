import React, { useState } from "react";
import {
  <PERSON><PERSON>,
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  Badge,
  Separator,
  Layout,
  useToastr,
  Toast
} from "@repo/ui";

const ToastDemo: React.FC = () => {
  const { 
    showToast, 
    errorToast, 
    warningToast, 
    onError, 
    onSucces, 
    toastrs,
    resetToast 
  } = useToastr();

  const [showDirectToast, setShowDirectToast] = useState(false);

  // Demo functions for different toast types
  const handleSuccessToast = () => {
    showToast({
      variant: "success",
      title: "Success!",
      content: "Your action was completed successfully.",
      delay: 4000,
    });
  };

  const handleErrorToast = () => {
    showToast({
      variant: "danger",
      title: "Error!",
      content: "Something went wrong. Please try again.",
      delay: 4000,
    });
  };

  const handleWarningToast = () => {
    showToast({
      variant: "warning",
      title: "Warning!",
      content: "Please review your input before proceeding.",
      delay: 4000,
    });
  };

  const handleInfoToast = () => {
    showToast({
      variant: "info",
      title: "Information",
      content: "Here's some useful information for you.",
      delay: 4000,
    });
  };

  // Legacy API methods
  const handleLegacyErrorToast = () => {
    errorToast("This is a legacy error toast message.");
  };

  const handleLegacyWarningToast = () => {
    warningToast("This is a legacy warning toast message.");
  };

  const handleLegacyOnError = () => {
    onError({
      msg: "API request failed",
      statusCode: 500,
    });
  };

  const handleLegacyOnSuccess = () => {
    onSucces({
      title: "Operation Complete",
      content: "Your data has been saved successfully.",
    });
  };

  // Test error handling
  const handleStringError = () => {
    onError("Simple string error message");
  };

  const handleCanceledRequest = () => {
    onError({ msg: "Canceled Request" }); // Should not show
  };

  const handleDirectToastComponent = () => {
    setShowDirectToast(true);
    setTimeout(() => setShowDirectToast(false), 4000);
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 py-8">
      <Layout.Container>
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-8">
            <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">
              Toast Components Demo
            </h1>
            <p className="text-lg text-gray-600 dark:text-gray-400">
              Comprehensive demo of the useToastr hook and Toast component with legacy API compatibility
            </p>
          </div>

          {/* Current Toasts Display */}
          {toastrs.length > 0 && (
            <Card className="mb-8">
              <CardHeader>
                <CardTitle>Active Toasts ({toastrs.length})</CardTitle>
                <CardDescription>
                  These are the currently active toasts in the Redux-compatible state
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {toastrs.map((toast, index) => (
                    <div key={toast.id} className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                      <div className="flex items-center gap-3">
                        <Badge variant={toast.variant === "danger" ? "destructive" : "default"}>
                          {toast.variant}
                        </Badge>
                        <div>
                          <div className="font-medium">{toast.title}</div>
                          <div className="text-sm text-gray-600 dark:text-gray-400">{toast.content}</div>
                        </div>
                      </div>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => resetToast(index)}
                      >
                        Remove
                      </Button>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Basic Toast Variants */}
            <Card>
              <CardHeader>
                <CardTitle>Basic Toast Variants</CardTitle>
                <CardDescription>
                  Test all four toast variants with custom titles and content
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <Button onClick={handleSuccessToast} className="w-full" variant="default">
                  Show Success Toast
                </Button>
                <Button onClick={handleErrorToast} className="w-full" variant="destructive">
                  Show Error Toast
                </Button>
                <Button onClick={handleWarningToast} className="w-full" variant="outline">
                  Show Warning Toast
                </Button>
                <Button onClick={handleInfoToast} className="w-full" variant="secondary">
                  Show Info Toast
                </Button>
              </CardContent>
            </Card>

            {/* Legacy API Methods */}
            <Card>
              <CardHeader>
                <CardTitle>Legacy API Methods</CardTitle>
                <CardDescription>
                  Test the legacy useToastr API methods for backward compatibility
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <Button onClick={handleLegacyErrorToast} className="w-full" variant="destructive">
                  errorToast() Method
                </Button>
                <Button onClick={handleLegacyWarningToast} className="w-full" variant="outline">
                  warningToast() Method
                </Button>
                <Button onClick={handleLegacyOnError} className="w-full" variant="destructive">
                  onError() with Object
                </Button>
                <Button onClick={handleLegacyOnSuccess} className="w-full" variant="default">
                  onSucces() Method
                </Button>
              </CardContent>
            </Card>

            {/* Error Handling Tests */}
            <Card>
              <CardHeader>
                <CardTitle>Error Handling Tests</CardTitle>
                <CardDescription>
                  Test various error scenarios and edge cases
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <Button onClick={handleStringError} className="w-full" variant="destructive">
                  String Error
                </Button>
                <Button onClick={handleCanceledRequest} className="w-full" variant="outline">
                  Canceled Request (Should Not Show)
                </Button>
                <Button 
                  onClick={() => onError({ msg: undefined })} 
                  className="w-full" 
                  variant="destructive"
                >
                  Undefined Message
                </Button>
              </CardContent>
            </Card>

            {/* Direct Toast Component */}
            <Card>
              <CardHeader>
                <CardTitle>Direct Toast Component</CardTitle>
                <CardDescription>
                  Test the Toast component directly for legacy compatibility
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <Button onClick={handleDirectToastComponent} className="w-full" variant="secondary">
                  Show Direct Toast Component
                </Button>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  This uses the Toast component directly, which is useful for legacy code that imports Toast directly.
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Direct Toast Component Usage */}
          {showDirectToast && (
            <Toast
              variant="info"
              title="Direct Toast"
              content="This toast is rendered using the Toast component directly!"
              show={true}
              delay={4000}
              onClose={() => setShowDirectToast(false)}
            />
          )}

          <Separator className="my-8" />

          {/* Migration Compatibility Test */}
          <Card className="mb-8">
            <CardHeader>
              <CardTitle>Migration Compatibility Test</CardTitle>
              <CardDescription>
                Simulating how legacy modules from feedback-frontend would use the hook
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                  <h4 className="font-semibold mb-2">Legacy Module Pattern:</h4>
                  <pre className="text-sm text-gray-600 dark:text-gray-400 overflow-x-auto">
{`// Old module code (no changes needed):
import useToastr from "unmatched/modules/toastr/hook";

const MyComponent = () => {
  const { errorToast, onSucces, showToast } = useToastr();

  const handleError = () => {
    errorToast("Something went wrong");
  };

  const handleSuccess = () => {
    onSucces({ title: "Success!", content: "Done" });
  };

  return <button onClick={handleError}>Test</button>;
};`}
                  </pre>
                </div>

                <div className="flex gap-4">
                  <Button
                    onClick={() => {
                      // Simulate legacy module usage
                      const toastr = { errorToast, onSucces, showToast };
                      toastr.errorToast("Legacy module error simulation");
                    }}
                    variant="destructive"
                  >
                    Simulate Legacy Error
                  </Button>

                  <Button
                    onClick={() => {
                      // Simulate legacy module usage
                      const toastr = { errorToast, onSucces, showToast };
                      toastr.onSucces({
                        title: "Legacy Success",
                        content: "Module migrated successfully!"
                      });
                    }}
                    variant="default"
                  >
                    Simulate Legacy Success
                  </Button>
                </div>

                <div className="text-sm text-green-600 dark:text-green-400 font-medium">
                  ✅ Migration Test: All legacy API methods work without code changes
                </div>
              </div>
            </CardContent>
          </Card>

          {/* API Documentation */}
          <Card>
            <CardHeader>
              <CardTitle>API Documentation</CardTitle>
              <CardDescription>
                Complete API reference for the useToastr hook
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                <div>
                  <h4 className="font-semibold mb-2">Hook Return Values:</h4>
                  <ul className="list-disc list-inside space-y-1 text-sm text-gray-600 dark:text-gray-400">
                    <li><code>toastrs</code> - Array of active toast objects</li>
                    <li><code>showToast(config)</code> - Show custom toast</li>
                    <li><code>errorToast(content)</code> - Show error toast</li>
                    <li><code>warningToast(content)</code> - Show warning toast</li>
                    <li><code>onError(err)</code> - Handle error objects</li>
                    <li><code>onSucces(config)</code> - Show success toast</li>
                    <li><code>resetToast(index)</code> - Remove toast by index</li>
                  </ul>
                </div>
                
                <div>
                  <h4 className="font-semibold mb-2">Toast Variants:</h4>
                  <div className="flex gap-2 flex-wrap">
                    <Badge>success</Badge>
                    <Badge variant="destructive">danger</Badge>
                    <Badge variant="outline">warning</Badge>
                    <Badge variant="secondary">info</Badge>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </Layout.Container>
    </div>
  );
};

export default ToastDemo;
