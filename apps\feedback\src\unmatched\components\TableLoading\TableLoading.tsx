import { ReactElement } from "react";
import Table from "../Table/Table";
import Placeholder from "../Placeholder/Placeholder";

export default function TableLoading({
  pages,
  active,
  size,
  totalItems,
}: any): ReactElement {
  const getColumns = () => {
    const basic = [
      {
        key: 1,
        hasSort: false,
        label: <Placeholder width="w-[66.66%]" />,
      },
      { key: 2, label: <Placeholder width="w-[66.66%]" />, hasSort: false },
    ];

    return [
      ...basic,
      { key: 3, label: <Placeholder width="w-[66.66%]" />, hasSort: false },
      { key: 5, label: <Placeholder width="w-[66.66%]" />, hasSort: false },
      { key: 6, label: <Placeholder width="w-[66.66%]" />, hasSort: false },
    ];
  };

  const getRows = () => {
    return [{ id: 1 }, { id: 2 }, { id: 3 }, { id: 4 }].map(
      (item: any, index: number) => {
        const isEven = index % 2 === 0 || index === 0;
        return (
          <Table.Row even={!isEven} key={item.id}>
            <Table.Data width="30px">
              <Placeholder width="w-[66.66%]" />
            </Table.Data>
            <Table.Data width="70px">
              <Placeholder width="w-[66.66%]" />
            </Table.Data>
            <Table.Data>
              <Placeholder width="w-[66.66%]" />
            </Table.Data>
            <Table.Data>
              <Placeholder width="w-[66.66%]" />
            </Table.Data>
            <Table.Data>
              <Placeholder width="w-[66.66%]" />
            </Table.Data>
          </Table.Row>
        );
      }
    );
  };
  return (
    <Table
      columns={getColumns()}
      isLoading={false}
      rows={[{ id: 1 }, { id: 2 }, { id: 3 }, { id: 4 }]}
      customRows
      render={() => getRows()}
      hasPagination
      activePage={active}
      pages={pages}
      size={size}
      totalItems={totalItems}
    />
  );
}
