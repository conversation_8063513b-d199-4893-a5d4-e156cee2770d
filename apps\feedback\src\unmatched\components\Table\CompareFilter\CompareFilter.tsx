/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { JSX } from "react";
// import styled from "styled-components";
// import {
//   Dropdown,
//   // Badge
// } from "react-bootstrap";
// import Layout from "../../Layout/Layout";
// import Text from "../../Text/Text";
// import FormControl from "../../Form/FormControl/FormControl";
import Div from "../../Div";
import Dropdown from "../../Dropdown/Dropdown";
// import Icon from "../../Icon";
import { Text } from "@repo/ui/components/Text";
import { ChevronDownIcon, ChevronUpIcon } from "lucide-react";
import Search from "@repo/ui/components/search";
// import Button from "../../Button/Button";
// import Span from "../../Span";
// import PropTypes from 'prop-types'
import styled from "styled-components";

// const DropdownMenu = styled(Dropdown.Menu)`
//   padding: 0px;
//   min-width: 5rem;
//   background: #ffffff;
//   /* white */

//   border: 1px solid #fbfbfb;
//   box-sizing: border-box;
//   box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.14);
//   border-radius: 2px;
// `;

const DropdownItem = styled(Dropdown.Item)`
  &:hover {
    background-color: #f9f9f9 !important;
  }
  &:focus {
    background-color: #f9f9f9 !important;
  }
`;

// const StyledBadge = styled(Badge)`
//   font-family: Inter;
//   font-style: normal;
//   font-weight: normal;
//   font-size: 12px;
//   background-color: #d2e1ff;
//   /* float: right; */
//   /* identical to box height */

//   /* text-grey-dark */

//   color: #2f2f2f;
// `;
interface Props {
  onSelect: (item: any) => void;
  selected: string;
  placeholder?: string;
  options: any;
  title: string | JSX.Element;
  hasSearch?: boolean;
  size?: any;
}
// interface Options {
//   title: string;
//   value: string;
//   id: string | number;
// }

const Filter = (props: Props) => {
  const [show, setShow] = React.useState(false);

  const getSearchTemplate = () => {
    if (!props.hasSearch) return;
    return (
      <DropdownItem onClick={() => ""} className="m-0 p-1">
        <Search placeholder="Search" onChange={() => {}} />
      </DropdownItem>
    );
  };

  return (
    <Dropdown
      // show={show}
      // className="h-100"
      // onToggle={() => {
      //   setShow(!show);
      // }}
    >
      <Dropdown.Toggle
        // block
        variant="ghost"
        size={props.size}
        className={"text-left border bg-white h-100"}
      >
        <Div className="row m-0">
          <Div className="col px-0 flex items-center">
            <Text.H4 className="mr-auto self-center">
              {props.title}
            </Text.H4>
          </Div>
          <Div
            className="col pr-0 flex items-center flex-row-reverse"
            style={{ maxWidth: "50%" }}
          >
            <Text.P2 className="self-center text-truncate">
              {props.selected ? (
                <Div className="text-black fs-12 text-truncate w-100 text-right">
                  {props.selected}
                </Div>
              ) : (
                // <StyledBadge variant="secondary">
                //   {props.selected}
                //   <Button variant="link" className="ml-2 text-muted p-0">
                //     <Icon icon="fal fa-times" />
                //   </Button>
                // </StyledBadge>
                <Div className="text-muted fs-12 text-truncate w-100 text-right">
                  {props.placeholder}
                </Div>
              )}
            </Text.P2>
          </Div>
          <Div
            className="col p-0 flex items-center flex-row-reverse text-right"
            style={{ maxWidth: 30 }}
          >
            {show ? (
              <ChevronUpIcon className="text-muted fs-12 self-center" />
            ) : (
              <ChevronDownIcon className="text-muted fs-12 self-center" />
            )}
          </Div>
        </Div>
      </Dropdown.Toggle>
      <Dropdown.Menu className="w-100">
        {getSearchTemplate()}
        {props.options.map((item: any) => {
          return (
            <Dropdown.Item
              as={Div}
              onClick={() => {
                props.onSelect(item);
                setShow(false);
              }}
              className="p-1 m-0 cursor-pointer"
              key={item.key}
            >
              <Text.P1>{item.title}</Text.P1>
            </Dropdown.Item>
          );
        })}
      </Dropdown.Menu>
    </Dropdown>
  );
};

// Filter.propTypes = {

// }

export default Filter;
