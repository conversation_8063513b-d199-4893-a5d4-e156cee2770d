"use client"

import * as React from "react"
import * as TooltipPrimitive from "@radix-ui/react-tooltip"

import { cn } from "../lib/utils"

function TooltipProvider({
  delayDuration = 0,
  ...props
}: React.ComponentProps<typeof TooltipPrimitive.Provider>) {
  return (
    <TooltipPrimitive.Provider
      data-slot="tooltip-provider"
      delayDuration={delayDuration}
      {...props}
    />
  )
}

function Tooltip({
  ...props
}: React.ComponentProps<typeof TooltipPrimitive.Root>) {
  return (
    <TooltipProvider>
      <TooltipPrimitive.Root data-slot="tooltip" {...props} />
    </TooltipProvider>
  )
}

function TooltipTrigger({
  ...props
}: React.ComponentProps<typeof TooltipPrimitive.Trigger>) {
  return <TooltipPrimitive.Trigger data-slot="tooltip-trigger" {...props} />
}

function TooltipContent({
  className,
  sideOffset = 0,
  children,
  ...props
}: React.ComponentProps<typeof TooltipPrimitive.Content>) {
  return (
    <TooltipPrimitive.Portal>
      <TooltipPrimitive.Content
        data-slot="tooltip-content"
        sideOffset={sideOffset}
        className={cn(
          "bg-primary text-primary-foreground animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-fit origin-(--radix-tooltip-content-transform-origin) rounded-md px-3 py-1.5 text-xs text-balance",
          className
        )}
        {...props}
      >
        {children}
        <TooltipPrimitive.Arrow className="bg-primary fill-primary z-50 size-2.5 translate-y-[calc(-50%_-_2px)] rotate-45 rounded-[2px]" />
      </TooltipPrimitive.Content>
    </TooltipPrimitive.Portal>
  )
}

// React Bootstrap compatible Tooltip component
interface BootstrapTooltipProps {
  id?: string;
  className?: string;
  children: React.ReactNode;
}

function BootstrapTooltip({ id, className, children, ...props }: BootstrapTooltipProps) {
  return (
    <div
      id={id}
      className={cn(
        "bg-gray-900 text-white text-xs rounded px-2 py-1 max-w-xs text-center",
        className
      )}
      role="tooltip"
      {...props}
    >
      {children}
    </div>
  )
}

// Placement mapping from Bootstrap to Radix
const placementMap = {
  top: "top" as const,
  bottom: "bottom" as const,
  left: "left" as const,
  right: "right" as const,
  "top-start": "top" as const,
  "top-end": "top" as const,
  "bottom-start": "bottom" as const,
  "bottom-end": "bottom" as const,
  "left-start": "left" as const,
  "left-end": "left" as const,
  "right-start": "right" as const,
  "right-end": "right" as const,
}

// React Bootstrap compatible OverlayTrigger component
interface OverlayTriggerProps {
  placement?: keyof typeof placementMap;
  overlay: React.ReactElement;
  children: React.ReactNode | ((props: any) => React.ReactNode);
  trigger?: string | string[];
  delay?: number | { show: number; hide: number };
  show?: boolean;
  onToggle?: (show: boolean) => void;
  defaultShow?: boolean;
}

function OverlayTrigger({
  placement = "top",
  overlay,
  children,
  trigger = "hover",
  delay = 0,
  show,
  onToggle,
  defaultShow = false,
  ...props
}: OverlayTriggerProps) {
  const [isOpen, setIsOpen] = React.useState(defaultShow)
  const isControlled = show !== undefined

  const handleOpenChange = (open: boolean) => {
    if (!isControlled) {
      setIsOpen(open)
    }
    onToggle?.(open)
  }

  const actualOpen = isControlled ? show : isOpen
  const side = placementMap[placement] || "top"

  // Handle delay
  const delayValue = typeof delay === 'number' ? delay : delay?.show || 0

  return (
    <TooltipProvider delayDuration={delayValue}>
      <TooltipPrimitive.Root open={actualOpen} onOpenChange={handleOpenChange} {...props}>
        <TooltipPrimitive.Trigger asChild>
          {typeof children === 'function' ? (
            <span>
              {children({
                ref: React.createRef(),
                onMouseEnter: () => handleOpenChange(true),
                onMouseLeave: () => handleOpenChange(false),
                onFocus: () => handleOpenChange(true),
                onBlur: () => handleOpenChange(false),
              })}
            </span>
          ) : (
            <span>{children}</span>
          )}
        </TooltipPrimitive.Trigger>
        <TooltipPrimitive.Portal>
          <TooltipPrimitive.Content
            side={side}
            sideOffset={4}
            className="z-50 animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95"
          >
            {overlay}
          </TooltipPrimitive.Content>
        </TooltipPrimitive.Portal>
      </TooltipPrimitive.Root>
    </TooltipProvider>
  )
}

export {
  Tooltip,
  TooltipTrigger,
  TooltipContent,
  TooltipProvider,
  BootstrapTooltip,
  OverlayTrigger
}
