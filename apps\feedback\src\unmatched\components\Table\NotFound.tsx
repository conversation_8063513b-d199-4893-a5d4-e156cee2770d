const NoRecords = ({ ...props }) => (
  <svg
    width={187}
    height={150}
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      d="M75 150c41.421 0 75-33.579 75-75S116.421 0 75 0 0 33.579 0 75s33.579 75 75 75Z"
      fill="url(#a)"
    />
    <g filter="url(#b)">
      <path
        d="M41 34h135a5.002 5.002 0 0 1 5 5v25a5.002 5.002 0 0 1-5 5H41a5 5 0 0 1-5-5V39a5 5 0 0 1 5-5Z"
        fill="#fff"
      />
      <path
        d="M104 42H78a3 3 0 1 0 0 6h26a3 3 0 1 0 0-6ZM122 55H78a3 3 0 1 0 0 6h44a3 3 0 1 0 0-6Z"
        fill="#F5FAFF"
      />
    </g>
    <path
      d="M68 51.5C68 43.492 61.508 37 53.5 37S39 43.492 39 51.5 45.492 66 53.5 66 68 59.508 68 51.5Z"
      fill="#1485FD"
    />
    <path
      d="M54.612 44.005c.357 0 .66.132.907.396.264.264.38.574.35.931l-.559 8.754c-.016.372-.163.69-.442.954a1.343 1.343 0 0 1-.955.373h-1.536c-.373 0-.691-.124-.955-.373a1.484 1.484 0 0 1-.442-.954l-.559-8.754a1.23 1.23 0 0 1 .35-.93 1.2 1.2 0 0 1 .907-.397h2.934Zm-.466 13.27c.357 0 .667.132.931.396s.396.574.396.931v1.071c0 .357-.132.667-.396.931a1.274 1.274 0 0 1-.931.396h-2.002c-.357 0-.667-.132-.931-.396a1.274 1.274 0 0 1-.396-.931v-1.07c0-.358.132-.668.396-.932s.574-.396.93-.396h2.003Z"
      fill="#fff"
    />
    <g filter="url(#c)">
      <path
        d="M161 79H26a5 5 0 0 0-5 5v25a5 5 0 0 0 5 5h135a5 5 0 0 0 5-5V84a5 5 0 0 0-5-5Z"
        fill="#fff"
      />
    </g>
    <path
      d="M90 87H64a3 3 0 1 0 0 6h26a3 3 0 1 0 0-6ZM108 100H64a3 3 0 1 0 0 6h44a3 3 0 1 0 0-6Z"
      fill="#F5FAFF"
    />
    <path
      d="M53 96.5C53 88.492 46.508 82 38.5 82S24 88.492 24 96.5 30.492 111 38.5 111 53 104.508 53 96.5Z"
      fill="#1485FD"
    />
    <path
      d="M39.612 89.005c.357 0 .66.132.907.396.264.264.38.574.35.931l-.559 8.754c-.016.372-.163.69-.442.954a1.34 1.34 0 0 1-.955.373h-1.536a1.34 1.34 0 0 1-.955-.373 1.483 1.483 0 0 1-.442-.954l-.559-8.754a1.23 1.23 0 0 1 .35-.93 1.2 1.2 0 0 1 .907-.397h2.934Zm-.466 13.27c.357 0 .667.132.931.396s.396.574.396.931v1.071c0 .357-.132.667-.396.931a1.274 1.274 0 0 1-.931.396h-2.002c-.357 0-.667-.132-.931-.396a1.274 1.274 0 0 1-.396-.931v-1.071c0-.357.132-.667.396-.931s.574-.396.93-.396h2.003Z"
      fill="#fff"
    />
    <defs>
      <filter
        id="b"
        x={30}
        y={31}
        width={157}
        height={47}
        filterUnits="userSpaceOnUse"
        colorInterpolationFilters="sRGB"
      >
        <feFlood floodOpacity={0} result="BackgroundImageFix" />
        <feColorMatrix
          in="SourceAlpha"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
          result="hardAlpha"
        />
        <feOffset dy={3} />
        <feGaussianBlur stdDeviation={3} />
        <feColorMatrix values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.161 0" />
        <feBlend
          in2="BackgroundImageFix"
          result="effect1_dropShadow_10833_9981"
        />
        <feBlend
          in="SourceGraphic"
          in2="effect1_dropShadow_10833_9981"
          result="shape"
        />
      </filter>
      <filter
        id="c"
        x={15}
        y={76}
        width={157}
        height={47}
        filterUnits="userSpaceOnUse"
        colorInterpolationFilters="sRGB"
      >
        <feFlood floodOpacity={0} result="BackgroundImageFix" />
        <feColorMatrix
          in="SourceAlpha"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
          result="hardAlpha"
        />
        <feOffset dy={3} />
        <feGaussianBlur stdDeviation={3} />
        <feColorMatrix values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.161 0" />
        <feBlend
          in2="BackgroundImageFix"
          result="effect1_dropShadow_10833_9981"
        />
        <feBlend
          in="SourceGraphic"
          in2="effect1_dropShadow_10833_9981"
          result="shape"
        />
      </filter>
      <linearGradient
        id="a"
        x1={75}
        y1={0}
        x2={75}
        y2={150}
        gradientUnits="userSpaceOnUse"
      >
        <stop stopColor="#E3ECFA" />
        <stop offset={1} stopColor="#DAE7FF" />
      </linearGradient>
    </defs>
  </svg>
)

export default NoRecords;
