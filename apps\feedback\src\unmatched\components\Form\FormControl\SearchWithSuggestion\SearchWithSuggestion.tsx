import React, { useEffect } from "react";
import _ from "lodash";
import Div from "../../../Div";
// import Icon from "../../../Icon";
import {
  FormControlProps,
  getFormControlAttributes,
  INPUT_TYPES,
  formControlDefaultProps,
} from "../form-control";
// import classes from "../../Forms.module.scss";
import { useDebounce } from "react-use";
import styled from "styled-components";
import { SearchIcon } from "lucide-react";

const SuggestionBox = styled.div`
  z-index: 9999;
  position: absolute;
  width: 100%;
  padding: 10px;
  top: 45px;
  background: #ffffff;
  border: 1px solid #dadada;
  box-sizing: border-box;
  box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.1);
  border-radius: 2px;
  .label {
    font-size: 12px;
    color: #838383;
  }
  .suggest {
    font-size: 16px;
    /* margin: 10px 0; */
    color: #000;
    display: block;
    padding: 5px 0;
    &:hover {
      background: #e6e6e6;
    }
  }
`;

const SuggestionItem = styled.div`
  cursor: pointer;
  padding: 5px;
  &:hover {
    background-color: #518cff;
    color: #fff;
  }
`;
interface SearchProps extends FormControlProps {
  onSearch?: Function;
  setValue?: Function;
  delay?: number;
}

const Item = (props: any) => {
  return <SuggestionItem {...props}>{props.children}</SuggestionItem>;
};

const SearchWithSuggestion: any = React.forwardRef(
  (props: SearchProps, ref: any) => {
    // const { list } = props;
    const _computed = getFormControlAttributes(props, ref);
    // const [value, setValue] = React.useState("");
    // const [list, setList] = React.useState([]);
    const [focus, setFocus] = React.useState(false);
    const { value, setValue } = props;

    useEffect(() => {
      window.addEventListener("click", disableFocus);
      return () => {
        window.removeEventListener("click", disableFocus);
      };
    });

    const disableFocus = () => {
      setFocus(false);
    };

    useDebounce(
      () => {
        if (value) {
          setFocus(true);
        }
        if (props.onSearch) props.onSearch(value);
      },
      props.delay || 100,
      [value]
    );

    const attributes = _.omit(
      {
        ..._computed,
        className: `${_computed.className} pl-4`,
        type: INPUT_TYPES.TEXT,
        onChange: (e: any) => {
          setValue && setValue(e.target.value);
          if (props.onChange) props.onChange(e);
        },
        onBlur: (e: any) => {
          setValue && setValue(e.target.value);
          if (props.onBlur) props.onBlur(e);
        },
      },
      ["onSearch", "delay"]
    );

    return (
      <Div
        className={"search-container"}
        id="search"
        style={{ position: "relative" }}
      >
        <Div className={"search-icon"}>
          {/* <Icon icon="fal fa-search" /> */}
          <SearchIcon className="inline-block size-4" />
        </Div>
        <input
          {...attributes}
          // onFocus={() => setFocus(true)}
          onBlur={() => setValue && setValue("")}
        />
        {focus ? (
          <SuggestionBox>
            <p className="label mb-0">Suggestions</p>
            {props.children}
          </SuggestionBox>
        ) : (
          ""
        )}
      </Div>
    );
  }
);

SearchWithSuggestion.defaultProps = { ...formControlDefaultProps, delay: 3000 };

SearchWithSuggestion.Item = Item;

export default SearchWithSuggestion;
