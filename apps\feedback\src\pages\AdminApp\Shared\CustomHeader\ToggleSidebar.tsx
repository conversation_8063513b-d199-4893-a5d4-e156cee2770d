import React from "react";
import { Button, Icon } from "unmatched/components";

const ToggleSidebar = (props: any) => {
  const { margin, sidebar, setSidebar } = props;
  return !margin ? (
    <Button
      variant="link"
      className="pt-0 mr-2 fs-20"
      onClick={() => {
        setSidebar(!sidebar);
      }}
    >
      <Icon icon={"far fa-bars"} variant="dark" />
    </Button>
  ) : null;
};

export default ToggleSidebar;
