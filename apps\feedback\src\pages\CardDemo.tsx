import React from "react";
import { <PERSON>, Layout, <PERSON><PERSON>, Text } from "../unmatched/components";

const CardDemo: React.FC = () => {
  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 py-8">
      <div className="max-w-7xl mx-auto px-4">
        <h1 className="text-4xl font-bold text-center mb-8 text-gray-900 dark:text-white">
          Card Components Demo
        </h1>
        
        {/* Basic Card Demo */}
        <section className="mb-12">
          <h2 className="text-2xl font-semibold mb-4 text-gray-800 dark:text-gray-200">
            Basic Card Component
          </h2>
          
          <Layout.Container>
            <Layout.Row>
              <Layout.Col md={6}>
                <div className="mb-6">
                  <h3 className="text-lg font-medium mb-2 text-gray-700 dark:text-gray-300">
                    Simple Card
                  </h3>
                  <Card className="mb-4">
                    <Card.Body>
                      <p className="text-gray-800 dark:text-gray-200">
                        This is a simple card with just a body. It maintains the same API as the original 
                        unmatched-core-ui Card component but uses Tailwind CSS for styling.
                      </p>
                    </Card.Body>
                  </Card>
                </div>
              </Layout.Col>
              
              <Layout.Col md={6}>
                <div className="mb-6">
                  <h3 className="text-lg font-medium mb-2 text-gray-700 dark:text-gray-300">
                    Card without Shadow
                  </h3>
                  <Card noShadow className="mb-4 border-2 border-dashed border-gray-300">
                    <Card.Body>
                      <p className="text-gray-800 dark:text-gray-200">
                        This card has the <code className="bg-gray-200 px-1 rounded">noShadow</code> prop 
                        set to true, removing the default shadow effect.
                      </p>
                    </Card.Body>
                  </Card>
                </div>
              </Layout.Col>
            </Layout.Row>
          </Layout.Container>
        </section>

        {/* Card with Header, Title, Body, Footer */}
        <section className="mb-12">
          <h2 className="text-2xl font-semibold mb-4 text-gray-800 dark:text-gray-200">
            Complete Card Structure
          </h2>
          
          <Layout.Container>
            <Layout.Row>
              <Layout.Col md={8} className="mx-auto">
                <Card className="mb-4">
                  <Card.Header className="px-4 py-3">
                    <div className="flex items-center justify-between">
                      <Text.H2>Card Header</Text.H2>
                      <span className="text-sm text-gray-500">Header Action</span>
                    </div>
                  </Card.Header>
                  
                  <Card.Body>
                    <Card.Title>Card Title Component</Card.Title>
                    <p className="text-gray-600 dark:text-gray-400 mb-4">
                      This card demonstrates all the sub-components: Header, Title, Body, and Footer. 
                      Each component maintains the same props and behavior as the original Bootstrap-based version.
                    </p>
                    <p className="text-gray-600 dark:text-gray-400">
                      The styling has been converted from Bootstrap classes to equivalent Tailwind CSS classes 
                      while preserving the exact same visual appearance and responsive behavior.
                    </p>
                  </Card.Body>
                  
                  <Card.Footer>
                    <Button className="mr-2">Cancel</Button>
                    <Button className="bg-blue-600 text-white hover:bg-blue-700">Save Changes</Button>
                  </Card.Footer>
                </Card>
              </Layout.Col>
            </Layout.Row>
          </Layout.Container>
        </section>

        {/* Interactive Card Demo */}
        <section className="mb-12">
          <h2 className="text-2xl font-semibold mb-4 text-gray-800 dark:text-gray-200">
            Interactive Card
          </h2>
          
          <Layout.Container>
            <Layout.Row>
              <Layout.Col md={4}>
                <Card 
                  className="mb-4 cursor-pointer hover:shadow-xl transition-shadow duration-200" 
                  onClick={() => alert('Card clicked!')}
                >
                  <Card.Body>
                    <Card.Title>Clickable Card</Card.Title>
                    <p className="text-gray-600 dark:text-gray-400">
                      This card has an onClick handler. Click anywhere on the card to trigger the action.
                    </p>
                  </Card.Body>
                </Card>
              </Layout.Col>
              
              <Layout.Col md={4}>
                <Card className="mb-4">
                  <Card.Body>
                    <Card.Title>Regular Card</Card.Title>
                    <p className="text-gray-600 dark:text-gray-400">
                      This is a regular card without any click handlers for comparison.
                    </p>
                  </Card.Body>
                </Card>
              </Layout.Col>
              
              <Layout.Col md={4}>
                <Card className="mb-4 bg-gradient-to-br from-blue-50 to-indigo-100 border-blue-200">
                  <Card.Body>
                    <Card.Title>Custom Styled Card</Card.Title>
                    <p className="text-gray-600 dark:text-gray-400">
                      This card has custom styling applied via className prop.
                    </p>
                  </Card.Body>
                </Card>
              </Layout.Col>
            </Layout.Row>
          </Layout.Container>
        </section>

        {/* Typography Demo in Cards */}
        <section className="mb-12">
          <h2 className="text-2xl font-semibold mb-4 text-gray-800 dark:text-gray-200">
            Typography in Cards
          </h2>
          
          <Layout.Container>
            <Layout.Row>
              <Layout.Col md={6}>
                <Card className="mb-4">
                  <Card.Body>
                    <Card.Title>Text Components Demo</Card.Title>
                    <div className="space-y-2">
                      <Text.H1>Heading 1</Text.H1>
                      <Text.H2>Heading 2</Text.H2>
                      <Text.H3>Heading 3</Text.H3>
                      <Text.H4>Heading 4</Text.H4>
                      <Text.P1>Paragraph 1 - Regular text content</Text.P1>
                      <Text.P2>Paragraph 2 - Smaller text content</Text.P2>
                      <Text.InputLabel>Input Label - Form label text</Text.InputLabel>
                    </div>
                  </Card.Body>
                </Card>
              </Layout.Col>
              
              <Layout.Col md={6}>
                <Card className="mb-4">
                  <Card.Body>
                    <Card.Title>Responsive Padding Demo</Card.Title>
                    <p className="text-gray-600 dark:text-gray-400 mb-4">
                      The Card.Body component includes responsive padding that matches the original Bootstrap implementation:
                    </p>
                    <ul className="text-sm text-gray-600 dark:text-gray-400 space-y-1">
                      <li>• Base: p-2 pb-3 (8px padding, 12px bottom)</li>
                      <li>• XL screens: p-4 (16px padding)</li>
                      <li>• Large screens: p-4 (16px padding)</li>
                      <li>• Medium screens: p-3 (12px padding)</li>
                      <li>• Small screens: p-3 (12px padding)</li>
                      <li>• Extra small: p-3 (12px padding)</li>
                    </ul>
                  </Card.Body>
                </Card>
              </Layout.Col>
            </Layout.Row>
          </Layout.Container>
        </section>

        {/* Usage Instructions */}
        <section className="mb-12">
          <h2 className="text-2xl font-semibold mb-4 text-gray-800 dark:text-gray-200">
            Usage Instructions
          </h2>
          
          <Layout.Container>
            <Card>
              <Card.Body>
                <Card.Title>Import and Use</Card.Title>
                <pre className="bg-gray-900 text-green-400 p-4 rounded-md text-sm overflow-x-auto mt-4">
{`import { Card } from "../unmatched/components";

// Basic usage - exactly like the original unmatched-core-ui
<Card>
  <Card.Body>
    <Card.Title>My Card Title</Card.Title>
    <p>Card content goes here</p>
  </Card.Body>
</Card>

// With all components
<Card onClick={() => console.log('clicked')} noShadow>
  <Card.Header className="custom-header">
    Header content
  </Card.Header>
  <Card.Body>
    <Card.Title>Title</Card.Title>
    Body content
  </Card.Body>
  <Card.Footer>
    Footer content
  </Card.Footer>
</Card>`}
                </pre>
                
                <div className="mt-4">
                  <h4 className="font-medium text-gray-800 dark:text-gray-200 mb-2">
                    Available Props:
                  </h4>
                  <ul className="text-sm text-gray-600 dark:text-gray-400 space-y-1">
                    <li><strong>Card:</strong> className, noShadow (boolean), onClick (function), style (object)</li>
                    <li><strong>Card.Header:</strong> className, style</li>
                    <li><strong>Card.Title:</strong> className (automatically applies text-primary pb-2)</li>
                    <li><strong>Card.Body:</strong> className (includes responsive padding)</li>
                    <li><strong>Card.Footer:</strong> className (includes text-right py-3)</li>
                  </ul>
                </div>
              </Card.Body>
            </Card>
          </Layout.Container>
        </section>
      </div>
    </div>
  );
};

export default CardDemo;
