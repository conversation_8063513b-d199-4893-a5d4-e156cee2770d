/* eslint-disable @typescript-eslint/no-explicit-any */
import { But<PERSON> } from "@repo/ui";
import {
  DropdownMenu as DM,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@repo/ui/components/dropdown-menu";

const Dropdown = (props: any) => {
  return <DM {...props} />;
};

function Toggle({ children, ...props }: any) {
  return (
    <DropdownMenuTrigger asChild>
      <Button {...props}>{children}</Button>
    </DropdownMenuTrigger>
  );
}

function Menu({ ...props }: any) {
  return <DropdownMenuContent {...props} />;
}

function Item({ children, ...props }: any) {
  return <DropdownMenuItem {...props}>{children}</DropdownMenuItem>;
}

Dropdown.Toggle = Toggle;
Dropdown.Menu = Menu;
Dropdown.Item = Item;

export default Dropdown;
