/* eslint-disable @typescript-eslint/no-explicit-any */
import { DownloadIcon } from "lucide-react";
import { Button, Layout, Table } from "unmatched/components";
// import PropTypes from 'prop-types'

const PeopleFilters = (props: any) => {
  const { year } = props;

  return (
    <Layout.Row className="py-2">
      <Layout.Col xl={2} lg={2} sm={3} xs={12} className="mt-3 px-2">
        <Table.Filter
          title="Year"
          selected={year.selected}
          options={year.options}
          onSelect={year.onSelect}
        />
      </Layout.Col>
      <Layout.Col xl={2} lg={2} sm={3} xs={12} className="mt-3 px-2">
        {props.children}
      </Layout.Col>
      {/* <Layout.Col xl={2} lg={2} sm={3} xs={12} className="mt-3 px-2">
        <Table.Filter
          title="Name"
          selected={name.selected}
          options={name.options}
          onOptionsSelect={name.onSelect}
        />
      </Layout.Col>
      <Layout.Col xl={2} lg={2} sm={3} xs={12} className="mt-3 px-2">
        <Table.Filter
          title="EmpId"
          selected={empId.selected}
          options={empId.options}
          onOptionsSelect={empId.onSelect}
        />
      </Layout.Col> */}
      <Layout.Col className="text-right self-center mt-3">
        <Button
          variant="outline-primary"
          onClick={() => (props.onDownload ? props.onDownload() : "")}
        >
          Download XLSX <DownloadIcon className="ml-2 inline-block size-5" /> {/* <Icon icon="fas fa-file-download" /> */}
        </Button>
      </Layout.Col>
    </Layout.Row>
  );
};

export default PeopleFilters;
