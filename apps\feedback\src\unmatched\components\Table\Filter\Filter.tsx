/* eslint-disable @typescript-eslint/no-explicit-any */
import React from "react";
// import styled from "styled-components";
// import { Dropdown } from "react-bootstrap";
// import Layout from "../../Layout/Layout";
// import Text from "../../Text/Text";
// import FormControl from "../../Form/FormControl/FormControl";
import Div from "../../Div";
import { Text } from "@repo/ui/components/Text";
import { ChevronDownIcon, ChevronUpIcon } from "lucide-react";
import Dropdown from "../../Dropdown/Dropdown";
import styled from "styled-components";
import Search from "@repo/ui/components/search";

// import Icon from "../../Icon";
// import PropTypes from 'prop-types'

// const DropdownMenu = styled(Dropdown.Menu)`
//   padding: 0px;
//   min-width: 5rem;
//   background: #ffffff;
//   /* white */

//   border: 1px solid #fbfbfb;
//   box-sizing: border-box;
//   box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.14);
//   border-radius: 2px;
// `;

const DropdownItem = styled(Dropdown.Item)`
  &:hover {
    background-color: #518cff !important;
    color: #fff !important;
  }
  &:focus {
    background-color: #518cff !important;
    color: #fff !important;
  }
`;

const Filter = (props: any) => {
  const [show, setShow] = React.useState(false);

  const getSearchTemplate = () => {
    if (!props.hasSearch) return;
    return (
      <DropdownItem onClick={() => ""} className="m-0 p-1">
        {/* <FormControl.Search placeholder="Search" /> */}
        <Search placeholder="Search" onChange={() => {}} />
      </DropdownItem>
    );
  };

  const label = props.selectedLabel || props.selected || "All";

  return (
    <Dropdown
      show={show}
      onToggle={() => {
        setShow(!show);
      }}
    >
      <Dropdown.Toggle
        block
        variant="white"
        className={"text-left border py-1 bg-white dark:bg-black mb-2 shadow"}
      >
        <Div className="row m-0 flex-nowrap gap-2">
          <Div className="px-0 flex items-center" style={{maxWidth: props.title.length * 5 + 20}}>
            <Text.H4 className="mr-auto fs-12 !mb-0 self-center">
              {props.title}
            </Text.H4>
          </Div>
          <Div className="flex-grow-1"/>
          <Div className="p-1 flex items-center justify-end">
            <Text.P2
              title={label}
              className="fs-12 text-truncate"
            >
              {label}
            </Text.P2>
          </Div>
          <Div className="p-0 flex items-center flex-row-reverse text-right" style={{maxWidth: 18}}>
            {/* <Icon
              className="text-muted fs-12 self-center"
              icon={show ? "fal fa-chevron-up" : "fal fa-chevron-down"}
            /> */}
            {show ? <ChevronUpIcon className="text-primary fs-12 self-center" /> : <ChevronDownIcon className="text-primary fs-12 self-center" />}
          </Div>
        </Div>
      </Dropdown.Toggle>
      <Dropdown.Menu className="w-100">
        {getSearchTemplate()}
        {props.options.map((item: any, index: any) => {
          return (
            <DropdownItem
              as={Div}
              onClick={() => {
                props.onSelect(item);
                setShow(false);
              }}
              className="p-1 m-0 cursor-pointer"
              key={item.key || `filter-${index}`}
            >
              <Text.P1>{item.title}</Text.P1>
            </DropdownItem>
          );
        })}
      </Dropdown.Menu>
    </Dropdown>
  );
};

// Filter.propTypes = {

// }

export default Filter;
