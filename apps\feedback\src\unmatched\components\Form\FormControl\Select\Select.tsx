/* eslint-disable @typescript-eslint/no-explicit-any */
import { ReactNode, useState } from "react";
// import { Dropdown } from "react-bootstrap";
import { Text } from "@repo/ui/components/Text";
// import Icon from "../../../Icon";
import Div from "../../../Div";
import Dropdown from "../../../Dropdown/Dropdown";
import { ChevronDownIcon, ChevronUpIcon } from "lucide-react";

// import styles from "./Select.module.scss";

interface SelectProps {
  onSelect?: (eventKey: any) => void;
  children: ReactNode;
  className?: ReactNode;
  value?: string;
  variant?: string;
  placeholder?: string;
}

const popperConfig = {
  modifiers: [
    {
      name: "offset",
      options: {
        offset: [0, 10],
      },
    },
  ],
};

const Select = (props: SelectProps) => {
  const { onSelect, children, value, variant, className, placeholder } = props;
  const [show, setShow] = useState(false);

  const onChange = () => {
    setShow(!show);
    if (onSelect) onSelect(!show);
  };

  return (
    <Dropdown show={show} onToggle={onChange}>
      <Dropdown.Toggle
        variant={variant || "light"}
        block
        size="sm"
        className={`dropdown-toggle text-left border rounded`}
      >
        <Div className="row m-0">
          <Div className="col-10 px-1">
            <Text.P1
              className={`float-left text-capitalize ${
                className || ""
              } text-truncate w-100`}
            >
              <span className="fs-12">{value || placeholder}</span>
            </Text.P1>
          </Div>
          <Div className="col-2 px-0">
            <Div className="pl-4 mr-2 float-right">
              {/* <Icon
                icon={
                  show ? "far fa-chevron-up fs-12" : "far fa-chevron-down fs-12"
                }
              /> */}
              {show ? (
                <ChevronUpIcon className="text-muted fs-12 self-center" />
              ) : (
                <ChevronDownIcon className="text-muted fs-12 self-center" />
              )}
            </Div>
          </Div>
        </Div>
      </Dropdown.Toggle>
      <Dropdown.Menu className="w-100 shadow" popperConfig={popperConfig}>
        {children}
      </Dropdown.Menu>
    </Dropdown>
  );
};

export const SelectItem = (props: SelectProps) => {
  const { children, onSelect, className } = props;
  return (
    <Dropdown.Item
      className={`dropdown-item fs-14 p-1 pl-2 ${className}`}
      onSelect={(eventKey: any) => (onSelect ? onSelect(eventKey) : null)}
    >
      {children}
    </Dropdown.Item>
  );
};

export default Select;
